{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya/lipya1/src/lib/api.ts"], "sourcesContent": ["// API Configuration and Base Functions\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';\n\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  message?: string;\n  data?: T;\n  pagination?: {\n    current_page: number;\n    last_page: number;\n    per_page: number;\n    total: number;\n  };\n}\n\nexport interface ApiError {\n  success: false;\n  message: string;\n  errors?: Record<string, string[]>;\n}\n\nclass ApiClient {\n  private baseURL: string;\n\n  constructor(baseURL: string) {\n    this.baseURL = baseURL;\n  }\n\n  private getAuthHeaders(): HeadersInit {\n    const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;\n    return {\n      'Content-Type': 'application/json',\n      'Accept': 'application/json',\n      ...(token && { 'Authorization': `Bearer ${token}` }),\n    };\n  }\n\n  private async handleResponse<T>(response: Response): Promise<ApiResponse<T>> {\n    const contentType = response.headers.get('content-type');\n    \n    if (!contentType || !contentType.includes('application/json')) {\n      throw new Error('Invalid response format');\n    }\n\n    const data = await response.json();\n\n    if (!response.ok) {\n      throw {\n        success: false,\n        message: data.message || 'An error occurred',\n        errors: data.errors || {},\n        status: response.status,\n      } as ApiError & { status: number };\n    }\n\n    return data;\n  }\n\n  async get<T>(endpoint: string, params?: Record<string, any>): Promise<ApiResponse<T>> {\n    const url = new URL(`${this.baseURL}${endpoint}`);\n    \n    if (params) {\n      Object.keys(params).forEach(key => {\n        if (params[key] !== undefined && params[key] !== null) {\n          url.searchParams.append(key, params[key].toString());\n        }\n      });\n    }\n\n    const response = await fetch(url.toString(), {\n      method: 'GET',\n      headers: this.getAuthHeaders(),\n    });\n\n    return this.handleResponse<T>(response);\n  }\n\n  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {\n    const response = await fetch(`${this.baseURL}${endpoint}`, {\n      method: 'POST',\n      headers: this.getAuthHeaders(),\n      body: data ? JSON.stringify(data) : undefined,\n    });\n\n    return this.handleResponse<T>(response);\n  }\n\n  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {\n    const response = await fetch(`${this.baseURL}${endpoint}`, {\n      method: 'PUT',\n      headers: this.getAuthHeaders(),\n      body: data ? JSON.stringify(data) : undefined,\n    });\n\n    return this.handleResponse<T>(response);\n  }\n\n  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {\n    const response = await fetch(`${this.baseURL}${endpoint}`, {\n      method: 'DELETE',\n      headers: this.getAuthHeaders(),\n    });\n\n    return this.handleResponse<T>(response);\n  }\n}\n\nexport const apiClient = new ApiClient(API_BASE_URL);\n\n// Helper function to handle API errors\nexport const handleApiError = (error: any): string => {\n  if (error?.message) {\n    return error.message;\n  }\n  \n  if (error?.errors) {\n    const firstError = Object.values(error.errors)[0];\n    if (Array.isArray(firstError) && firstError.length > 0) {\n      return firstError[0];\n    }\n  }\n  \n  return 'An unexpected error occurred';\n};\n\n// Helper function to check if user is authenticated\nexport const isAuthenticated = (): boolean => {\n  if (typeof window === 'undefined') return false;\n  return !!localStorage.getItem('auth_token');\n};\n\n// Helper function to get stored user data\nexport const getStoredUser = () => {\n  if (typeof window === 'undefined') return null;\n  const userData = localStorage.getItem('user_data');\n  return userData ? JSON.parse(userData) : null;\n};\n\n// Helper function to clear auth data\nexport const clearAuthData = () => {\n  if (typeof window === 'undefined') return;\n  localStorage.removeItem('auth_token');\n  localStorage.removeItem('user_data');\n};\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;;;;;AAClB;AAArB,MAAM,eAAe,oEAAmC;AAoBxD,MAAM;IACI,QAAgB;IAExB,YAAY,OAAe,CAAE;QAC3B,IAAI,CAAC,OAAO,GAAG;IACjB;IAEQ,iBAA8B;QACpC,MAAM,QAAQ,uCAAgC,aAAa,OAAO,CAAC;QACnE,OAAO;YACL,gBAAgB;YAChB,UAAU;YACV,GAAI,SAAS;gBAAE,iBAAiB,CAAC,OAAO,EAAE,OAAO;YAAC,CAAC;QACrD;IACF;IAEA,MAAc,eAAkB,QAAkB,EAA2B;QAC3E,MAAM,cAAc,SAAS,OAAO,CAAC,GAAG,CAAC;QAEzC,IAAI,CAAC,eAAe,CAAC,YAAY,QAAQ,CAAC,qBAAqB;YAC7D,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM;gBACJ,SAAS;gBACT,SAAS,KAAK,OAAO,IAAI;gBACzB,QAAQ,KAAK,MAAM,IAAI,CAAC;gBACxB,QAAQ,SAAS,MAAM;YACzB;QACF;QAEA,OAAO;IACT;IAEA,MAAM,IAAO,QAAgB,EAAE,MAA4B,EAA2B;QACpF,MAAM,MAAM,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;QAEhD,IAAI,QAAQ;YACV,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAA;gBAC1B,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,MAAM,CAAC,IAAI,KAAK,MAAM;oBACrD,IAAI,YAAY,CAAC,MAAM,CAAC,KAAK,MAAM,CAAC,IAAI,CAAC,QAAQ;gBACnD;YACF;QACF;QAEA,MAAM,WAAW,MAAM,MAAM,IAAI,QAAQ,IAAI;YAC3C,QAAQ;YACR,SAAS,IAAI,CAAC,cAAc;QAC9B;QAEA,OAAO,IAAI,CAAC,cAAc,CAAI;IAChC;IAEA,MAAM,KAAQ,QAAgB,EAAE,IAAU,EAA2B;QACnE,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU,EAAE;YACzD,QAAQ;YACR,SAAS,IAAI,CAAC,cAAc;YAC5B,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;QAEA,OAAO,IAAI,CAAC,cAAc,CAAI;IAChC;IAEA,MAAM,IAAO,QAAgB,EAAE,IAAU,EAA2B;QAClE,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU,EAAE;YACzD,QAAQ;YACR,SAAS,IAAI,CAAC,cAAc;YAC5B,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;QAEA,OAAO,IAAI,CAAC,cAAc,CAAI;IAChC;IAEA,MAAM,OAAU,QAAgB,EAA2B;QACzD,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU,EAAE;YACzD,QAAQ;YACR,SAAS,IAAI,CAAC,cAAc;QAC9B;QAEA,OAAO,IAAI,CAAC,cAAc,CAAI;IAChC;AACF;AAEO,MAAM,YAAY,IAAI,UAAU;AAGhC,MAAM,iBAAiB,CAAC;IAC7B,IAAI,OAAO,SAAS;QAClB,OAAO,MAAM,OAAO;IACtB;IAEA,IAAI,OAAO,QAAQ;QACjB,MAAM,aAAa,OAAO,MAAM,CAAC,MAAM,MAAM,CAAC,CAAC,EAAE;QACjD,IAAI,MAAM,OAAO,CAAC,eAAe,WAAW,MAAM,GAAG,GAAG;YACtD,OAAO,UAAU,CAAC,EAAE;QACtB;IACF;IAEA,OAAO;AACT;AAGO,MAAM,kBAAkB;IAC7B,uCAAmC;;IAAY;IAC/C,OAAO,CAAC,CAAC,aAAa,OAAO,CAAC;AAChC;AAGO,MAAM,gBAAgB;IAC3B,uCAAmC;;IAAW;IAC9C,MAAM,WAAW,aAAa,OAAO,CAAC;IACtC,OAAO,WAAW,KAAK,KAAK,CAAC,YAAY;AAC3C;AAGO,MAAM,gBAAgB;IAC3B,uCAAmC;;IAAM;IACzC,aAAa,UAAU,CAAC;IACxB,aAAa,UAAU,CAAC;AAC1B", "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya/lipya1/src/services/auth.ts"], "sourcesContent": ["import { apiClient, ApiResponse } from '../lib/api';\n\nexport interface User {\n  id: number;\n  name: string;\n  email: string;\n  role: string;\n}\n\nexport interface LoginRequest {\n  email: string;\n  password: string;\n}\n\nexport interface RegisterRequest {\n  name: string;\n  email: string;\n  password: string;\n  password_confirmation: string;\n}\n\nexport interface AuthResponse {\n  token: string;\n  token_type: string;\n  expires_in: number;\n  user: User;\n}\n\nexport const authService = {\n  // Login user\n  async login(credentials: LoginRequest): Promise<ApiResponse<AuthResponse>> {\n    const response = await apiClient.post<AuthResponse>('/auth/login', credentials);\n    \n    if (response.success && response.data) {\n      // Store token and user data in localStorage\n      localStorage.setItem('auth_token', response.data.token);\n      localStorage.setItem('user_data', JSON.stringify(response.data.user));\n    }\n    \n    return response;\n  },\n\n  // Register new user\n  async register(userData: RegisterRequest): Promise<ApiResponse<AuthResponse>> {\n    const response = await apiClient.post<AuthResponse>('/auth/register', userData);\n    \n    if (response.success && response.data) {\n      // Store token and user data in localStorage\n      localStorage.setItem('auth_token', response.data.token);\n      localStorage.setItem('user_data', JSON.stringify(response.data.user));\n    }\n    \n    return response;\n  },\n\n  // Logout user\n  async logout(): Promise<ApiResponse> {\n    try {\n      const response = await apiClient.post('/auth/logout');\n      return response;\n    } finally {\n      // Always clear local storage, even if API call fails\n      localStorage.removeItem('auth_token');\n      localStorage.removeItem('user_data');\n    }\n  },\n\n  // Get current user info\n  async me(): Promise<ApiResponse<User>> {\n    return apiClient.get<User>('/auth/me');\n  },\n\n  // Refresh token\n  async refresh(): Promise<ApiResponse<AuthResponse>> {\n    const response = await apiClient.post<AuthResponse>('/auth/refresh');\n    \n    if (response.success && response.data) {\n      // Update stored token\n      localStorage.setItem('auth_token', response.data.token);\n      localStorage.setItem('user_data', JSON.stringify(response.data.user));\n    }\n    \n    return response;\n  },\n\n  // Check if user is authenticated\n  isAuthenticated(): boolean {\n    if (typeof window === 'undefined') return false;\n    return !!localStorage.getItem('auth_token');\n  },\n\n  // Get stored user data\n  getUser(): User | null {\n    if (typeof window === 'undefined') return null;\n    const userData = localStorage.getItem('user_data');\n    return userData ? JSON.parse(userData) : null;\n  },\n\n  // Clear authentication data\n  clearAuth(): void {\n    if (typeof window === 'undefined') return;\n    localStorage.removeItem('auth_token');\n    localStorage.removeItem('user_data');\n  }\n};\n"], "names": [], "mappings": ";;;AAAA;;AA4BO,MAAM,cAAc;IACzB,aAAa;IACb,MAAM,OAAM,WAAyB;QACnC,MAAM,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,IAAI,CAAe,eAAe;QAEnE,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;YACrC,4CAA4C;YAC5C,aAAa,OAAO,CAAC,cAAc,SAAS,IAAI,CAAC,KAAK;YACtD,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC,SAAS,IAAI,CAAC,IAAI;QACrE;QAEA,OAAO;IACT;IAEA,oBAAoB;IACpB,MAAM,UAAS,QAAyB;QACtC,MAAM,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,IAAI,CAAe,kBAAkB;QAEtE,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;YACrC,4CAA4C;YAC5C,aAAa,OAAO,CAAC,cAAc,SAAS,IAAI,CAAC,KAAK;YACtD,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC,SAAS,IAAI,CAAC,IAAI;QACrE;QAEA,OAAO;IACT;IAEA,cAAc;IACd,MAAM;QACJ,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,IAAI,CAAC;YACtC,OAAO;QACT,SAAU;YACR,qDAAqD;YACrD,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;QAC1B;IACF;IAEA,wBAAwB;IACxB,MAAM;QACJ,OAAO,oHAAA,CAAA,YAAS,CAAC,GAAG,CAAO;IAC7B;IAEA,gBAAgB;IAChB,MAAM;QACJ,MAAM,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,IAAI,CAAe;QAEpD,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;YACrC,sBAAsB;YACtB,aAAa,OAAO,CAAC,cAAc,SAAS,IAAI,CAAC,KAAK;YACtD,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC,SAAS,IAAI,CAAC,IAAI;QACrE;QAEA,OAAO;IACT;IAEA,iCAAiC;IACjC;QACE,uCAAmC;;QAAY;QAC/C,OAAO,CAAC,CAAC,aAAa,OAAO,CAAC;IAChC;IAEA,uBAAuB;IACvB;QACE,uCAAmC;;QAAW;QAC9C,MAAM,WAAW,aAAa,OAAO,CAAC;QACtC,OAAO,WAAW,KAAK,KAAK,CAAC,YAAY;IAC3C;IAEA,4BAA4B;IAC5B;QACE,uCAAmC;;QAAM;QACzC,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;IAC1B;AACF", "debugId": null}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya/lipya1/src/context/UserContext.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from \"react\";\r\nimport { authService, User } from \"../services/auth\";\r\n\r\ntype UserContextType = {\r\n  user: User | null;\r\n  setUser: (user: User | null) => void;\r\n  login: (email: string, password: string) => Promise<{ success: boolean; message?: string }>;\r\n  register: (userData: { name: string; email: string; password: string; password_confirmation: string }) => Promise<{ success: boolean; message?: string }>;\r\n  logout: () => Promise<void>;\r\n  isLoading: boolean;\r\n  isAuthenticated: boolean;\r\n};\r\n\r\nconst UserContext = createContext<UserContextType | undefined>(undefined);\r\n\r\nexport function UserProvider({ children }: { children: ReactNode }) {\r\n  const [user, setUser] = useState<User | null>(null);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\r\n\r\n  // Check if user is authenticated on mount\r\n  useEffect(() => {\r\n    const checkAuth = async () => {\r\n      try {\r\n        if (authService.isAuthenticated()) {\r\n          const storedUser = authService.getUser();\r\n          if (storedUser) {\r\n            setUser(storedUser);\r\n            setIsAuthenticated(true);\r\n\r\n            // Verify token is still valid\r\n            try {\r\n              await authService.me();\r\n            } catch (error) {\r\n              // Token is invalid, clear auth data\r\n              authService.clearAuth();\r\n              setUser(null);\r\n              setIsAuthenticated(false);\r\n            }\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Auth check failed:', error);\r\n        authService.clearAuth();\r\n        setUser(null);\r\n        setIsAuthenticated(false);\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    checkAuth();\r\n  }, []);\r\n\r\n  const login = async (email: string, password: string) => {\r\n    try {\r\n      const response = await authService.login({ email, password });\r\n\r\n      if (response.success && response.data) {\r\n        setUser(response.data.user);\r\n        setIsAuthenticated(true);\r\n        return { success: true };\r\n      } else {\r\n        return { success: false, message: response.message || 'Login failed' };\r\n      }\r\n    } catch (error: any) {\r\n      return {\r\n        success: false,\r\n        message: error.message || 'Login failed'\r\n      };\r\n    }\r\n  };\r\n\r\n  const register = async (userData: { name: string; email: string; password: string; password_confirmation: string }) => {\r\n    try {\r\n      const response = await authService.register(userData);\r\n\r\n      if (response.success && response.data) {\r\n        setUser(response.data.user);\r\n        setIsAuthenticated(true);\r\n        return { success: true };\r\n      } else {\r\n        return { success: false, message: response.message || 'Registration failed' };\r\n      }\r\n    } catch (error: any) {\r\n      return {\r\n        success: false,\r\n        message: error.message || 'Registration failed'\r\n      };\r\n    }\r\n  };\r\n\r\n  const logout = async () => {\r\n    try {\r\n      await authService.logout();\r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n    } finally {\r\n      setUser(null);\r\n      setIsAuthenticated(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <UserContext.Provider value={{\r\n      user,\r\n      setUser,\r\n      login,\r\n      register,\r\n      logout,\r\n      isLoading,\r\n      isAuthenticated\r\n    }}>\r\n      {children}\r\n    </UserContext.Provider>\r\n  );\r\n}\r\n\r\nexport function useUser() {\r\n  const context = useContext(UserContext);\r\n  if (!context) {\r\n    throw new Error(\"useUser must be used within a UserProvider\");\r\n  }\r\n  return context;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAeA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAA2B;;IAChE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,0CAA0C;IAC1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;oDAAY;oBAChB,IAAI;wBACF,IAAI,0HAAA,CAAA,cAAW,CAAC,eAAe,IAAI;4BACjC,MAAM,aAAa,0HAAA,CAAA,cAAW,CAAC,OAAO;4BACtC,IAAI,YAAY;gCACd,QAAQ;gCACR,mBAAmB;gCAEnB,8BAA8B;gCAC9B,IAAI;oCACF,MAAM,0HAAA,CAAA,cAAW,CAAC,EAAE;gCACtB,EAAE,OAAO,OAAO;oCACd,oCAAoC;oCACpC,0HAAA,CAAA,cAAW,CAAC,SAAS;oCACrB,QAAQ;oCACR,mBAAmB;gCACrB;4BACF;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,sBAAsB;wBACpC,0HAAA,CAAA,cAAW,CAAC,SAAS;wBACrB,QAAQ;wBACR,mBAAmB;oBACrB,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;QACF;iCAAG,EAAE;IAEL,MAAM,QAAQ,OAAO,OAAe;QAClC,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,cAAW,CAAC,KAAK,CAAC;gBAAE;gBAAO;YAAS;YAE3D,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,QAAQ,SAAS,IAAI,CAAC,IAAI;gBAC1B,mBAAmB;gBACnB,OAAO;oBAAE,SAAS;gBAAK;YACzB,OAAO;gBACL,OAAO;oBAAE,SAAS;oBAAO,SAAS,SAAS,OAAO,IAAI;gBAAe;YACvE;QACF,EAAE,OAAO,OAAY;YACnB,OAAO;gBACL,SAAS;gBACT,SAAS,MAAM,OAAO,IAAI;YAC5B;QACF;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,cAAW,CAAC,QAAQ,CAAC;YAE5C,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,QAAQ,SAAS,IAAI,CAAC,IAAI;gBAC1B,mBAAmB;gBACnB,OAAO;oBAAE,SAAS;gBAAK;YACzB,OAAO;gBACL,OAAO;oBAAE,SAAS;oBAAO,SAAS,SAAS,OAAO,IAAI;gBAAsB;YAC9E;QACF,EAAE,OAAO,OAAY;YACnB,OAAO;gBACL,SAAS;gBACT,SAAS,MAAM,OAAO,IAAI;YAC5B;QACF;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,MAAM,0HAAA,CAAA,cAAW,CAAC,MAAM;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,QAAQ;YACR,mBAAmB;QACrB;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;YAC3B;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBACG;;;;;;AAGP;GArGgB;KAAA;AAuGT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 362, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya/lipya1/src/components/Navbar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport Image from \"next/image\";\r\nimport { Menu, X } from \"lucide-react\";\r\nimport { useUser } from \"../context/UserContext\"; // عدل حسب مكان الملف\r\nimport { useRouter } from \"next/navigation\";\r\nimport Page from \"@/app/committees/page\";\r\n function Navbar() {\r\n  const [menuOpen, setMenuOpen] = useState(false);\r\n  const [dropdownOpen, setDropdownOpen] = useState(false);\r\n  const toggleMenu = () => setMenuOpen(!menuOpen);\r\n\r\n  const { user, logout } = useUser();\r\n  const router = useRouter();\r\n\r\n  const dropdownRef = useRef<HTMLDivElement>(null);\r\n\r\n  // إغلاق الـ dropdown عند الضغط خارجها\r\n  useEffect(() => {\r\n    function handleClickOutside(event: MouseEvent) {\r\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\r\n        setDropdownOpen(false);\r\n      }\r\n    }\r\n    if (dropdownOpen) {\r\n      document.addEventListener(\"mousedown\", handleClickOutside);\r\n    } else {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    }\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, [dropdownOpen]);\r\n\r\n  const handleLogout = async () => {\r\n    try {\r\n      await logout();\r\n      setDropdownOpen(false);\r\n      router.push(\"/login\");\r\n    } catch (error) {\r\n      console.error('Logout failed:', error);\r\n      setDropdownOpen(false);\r\n      router.push(\"/login\");\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <nav className=\"absolute top-[35px] left-0 w-full bg-gray-800/30 text-white z-40 backdrop-blur-md border-b border-white/10\">\r\n  <div className=\"max-w-7xl mx-auto px-4 py-3 flex items-center justify-between h-16\">\r\n    {/* الشعار */}\r\n    <div className=\"flex items-center space-x-2 min-w-[60px]\">\r\n      <Image src=\"/logo.png\" alt=\"Logo\" width={50} height={40} />\r\n    </div>\r\n\r\n    {/* روابط سطح المكتب */}\r\n    <ul className=\"hidden md:flex space-x-4 text-sm uppercase font-medium flex-grow justify-center\">\r\n      <li><Link className=\"hover:text-orange-400 transition\" href=\"/\">Home</Link></li>\r\n      <li><Link className=\"hover:text-orange-400 transition\" href=\"/about\">About us</Link></li>\r\n      <li><Link className=\"hover:text-orange-400 transition\" href=\"/committees\">Committees</Link></li>\r\n      <li><Link className=\"hover:text-orange-400 transition\" href=\"/call-for-papers\">Call For Papers</Link></li>\r\n      <li><Link className=\"hover:text-orange-400 transition\" href=\"/submission\">Submission</Link></li>\r\n      <li><Link className=\"hover:text-orange-400 transition\" href=\"/registration\">Registration</Link></li> {/* Corrected href */}\r\n      <li><Link className=\"hover:text-orange-400 transition\" href=\"/venue\">Venue</Link></li>\r\n      <li>\r\n        <Link\r\n          href=\"/contact-us\"\r\n          scroll={true}\r\n          onClick={toggleMenu}\r\n          className=\"block bg-orange-500 hover:bg-orange-600 text-white px-3 py-1 rounded-md text-sm font-semibold\"\r\n        >\r\n          Contact Us\r\n        </Link>\r\n      </li>\r\n    </ul>\r\n\r\n    {/* صورة المستخدم أو زر تسجيل الدخول + زر القائمة للموبايل */}\r\n    <div className=\"flex items-center space-x-3 min-w-[100px] justify-end relative\">\r\n      {user ? (\r\n        <>\r\n          <button onClick={() => setDropdownOpen(!dropdownOpen)} className=\"focus:outline-none\">\r\n            <Image\r\n              src=\"/avatar.png\"\r\n              alt=\"User\"\r\n              width={36}\r\n              height={36}\r\n              className=\"rounded-full border border-white\"\r\n            />\r\n          </button>\r\n\r\n          {dropdownOpen && (\r\n            <div\r\n              ref={dropdownRef}\r\n              className=\"absolute right-0 mt-2 w-40 bg-gray-900 border border-gray-700 rounded shadow-lg z-50\"\r\n            >\r\n              <button\r\n                onClick={handleLogout}\r\n                className=\"block w-full text-left px-4 py-2 text-sm hover:bg-orange-600 hover:text-white text-red-500\"\r\n              >\r\n                تسجيل الخروج\r\n              </button>\r\n            </div>\r\n          )}\r\n        </>\r\n      ) : (\r\n        <Link\r\n          href=\"/login\"\r\n          className=\"text-sm bg-orange-500 hover:bg-orange-600 px-3 py-1 rounded text-white\"\r\n        >\r\n          Login\r\n        </Link>\r\n      )}\r\n\r\n      {/* زر القائمة للموبايل */}\r\n      <button\r\n        className=\"md:hidden text-white\"\r\n        onClick={toggleMenu}\r\n        aria-label=\"Toggle menu\"\r\n      >\r\n        {menuOpen ? <X size={28} /> : <Menu size={28} />}\r\n      </button>\r\n    </div>\r\n  </div>\r\n\r\n  {/* القائمة الجانبية للموبايل */}\r\n  {menuOpen && (\r\n    <div className=\"md:hidden flex flex-col px-6 pb-4 pt-2 space-y-4 bg-gray-800/30 text-white backdrop-blur-md border-t border-white/10\">\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/\" onClick={toggleMenu}>Home</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/about\" onClick={toggleMenu}>About us</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/committees\" onClick={toggleMenu}>Committees</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/call-for-papers\" onClick={toggleMenu}>Call For Papers</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/submission\" onClick={toggleMenu}>Submission</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/registration\" onClick={toggleMenu}>Registration</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/venue\" onClick={toggleMenu}>Venue</Link>\r\n      <Link\r\n        href=\"/contact-us\"\r\n        scroll={true}\r\n        onClick={toggleMenu}\r\n        className=\"block bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md text-sm font-semibold w-fit\"\r\n      >\r\n        Contact Us\r\n      </Link>\r\n    </div>\r\n  )}\r\n</nav>\r\n    \r\n    </>\r\n  );\r\n}\r\nexport default Navbar ; "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA,oOAAkD,qBAAqB;AACvE;;;AAPA;;;;;;;AASC,SAAS;;IACR,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,aAAa,IAAM,YAAY,CAAC;IAEtC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,SAAS,mBAAmB,KAAiB;gBAC3C,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;oBAC9E,gBAAgB;gBAClB;YACF;YACA,IAAI,cAAc;gBAChB,SAAS,gBAAgB,CAAC,aAAa;YACzC,OAAO;gBACL,SAAS,mBAAmB,CAAC,aAAa;YAC5C;YACA;oCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;2BAAG;QAAC;KAAa;IAEjB,MAAM,eAAe;QACnB,IAAI;YACF,MAAM;YACN,gBAAgB;YAChB,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,gBAAgB;YAChB,OAAO,IAAI,CAAC;QACd;IACF;IAEA,qBACE;kBACE,cAAA,6LAAC;YAAI,WAAU;;8BACnB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCAAC,KAAI;gCAAY,KAAI;gCAAO,OAAO;gCAAI,QAAQ;;;;;;;;;;;sCAIvD,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC;8CAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,WAAU;wCAAmC,MAAK;kDAAI;;;;;;;;;;;8CAChE,6LAAC;8CAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,WAAU;wCAAmC,MAAK;kDAAS;;;;;;;;;;;8CACrE,6LAAC;8CAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,WAAU;wCAAmC,MAAK;kDAAc;;;;;;;;;;;8CAC1E,6LAAC;8CAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,WAAU;wCAAmC,MAAK;kDAAmB;;;;;;;;;;;8CAC/E,6LAAC;8CAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,WAAU;wCAAmC,MAAK;kDAAc;;;;;;;;;;;8CAC1E,6LAAC;8CAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,WAAU;wCAAmC,MAAK;kDAAgB;;;;;;;;;;;gCAAwB;8CACpG,6LAAC;8CAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,WAAU;wCAAmC,MAAK;kDAAS;;;;;;;;;;;8CACrE,6LAAC;8CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,QAAQ;wCACR,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAOL,6LAAC;4BAAI,WAAU;;gCACZ,qBACC;;sDACE,6LAAC;4CAAO,SAAS,IAAM,gBAAgB,CAAC;4CAAe,WAAU;sDAC/D,cAAA,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;;;;;;wCAIb,8BACC,6LAAC;4CACC,KAAK;4CACL,WAAU;sDAEV,cAAA,6LAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;iEAOP,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAMH,6LAAC;oCACC,WAAU;oCACV,SAAS;oCACT,cAAW;8CAEV,yBAAW,6LAAC,+LAAA,CAAA,IAAC;wCAAC,MAAM;;;;;6DAAS,6LAAC,qMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;gBAM/C,0BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAI,SAAS;sCAAY;;;;;;sCACjF,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAS,SAAS;sCAAY;;;;;;sCACtF,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAc,SAAS;sCAAY;;;;;;sCAC3F,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAmB,SAAS;sCAAY;;;;;;sCAChG,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAc,SAAS;sCAAY;;;;;;sCAC3F,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAgB,SAAS;sCAAY;;;;;;sCAC7F,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAS,SAAS;sCAAY;;;;;;sCACtF,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,QAAQ;4BACR,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;AASP;GA7IU;;QAKiB,iIAAA,CAAA,UAAO;QACjB,qIAAA,CAAA,YAAS;;;KANhB;uCA8IK", "debugId": null}}, {"offset": {"line": 781, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya/lipya1/src/services/conferences.ts"], "sourcesContent": ["import { apiClient, ApiResponse } from '../lib/api';\n\nexport interface Conference {\n  id: number;\n  title: string;\n  subtitle: string;\n  description: string;\n  start_date: string;\n  end_date: string;\n  location: string;\n  venue: string;\n  logo_url: string;\n  banner_image_url: string;\n  registration_fee_local: number;\n  registration_fee_international: number;\n  currency_local: string;\n  currency_international: string;\n  is_active: boolean;\n  is_current: boolean;\n  featured_speakers?: Speaker[];\n  recent_news?: NewsItem[];\n}\n\nexport interface Speaker {\n  id: number;\n  name: string;\n  title: string;\n  bio: string;\n  image_url: string;\n  is_keynote: boolean;\n  is_committee: boolean;\n  is_featured: boolean;\n  order: number;\n}\n\nexport interface NewsItem {\n  id: number;\n  title: string;\n  slug: string;\n  excerpt: string;\n  featured_image_url: string;\n  published_at: string;\n  reading_time: string;\n  is_featured: boolean;\n}\n\nexport const conferenceService = {\n  // Get all conferences\n  async getConferences(): Promise<ApiResponse<Conference[]>> {\n    return apiClient.get<Conference[]>('/conferences');\n  },\n\n  // Get current active conference\n  async getCurrentConference(): Promise<ApiResponse<Conference>> {\n    return apiClient.get<Conference>('/conferences/current');\n  },\n\n  // Get specific conference by ID\n  async getConference(id: number): Promise<ApiResponse<Conference>> {\n    return apiClient.get<Conference>(`/conferences/${id}`);\n  },\n\n  // Get conference speakers\n  async getConferenceSpeakers(conferenceId: number): Promise<ApiResponse<Speaker[]>> {\n    return apiClient.get<Speaker[]>(`/conferences/${conferenceId}/speakers`);\n  },\n\n  // Get conference news\n  async getConferenceNews(conferenceId: number, params?: {\n    per_page?: number;\n    page?: number;\n  }): Promise<ApiResponse<NewsItem[]>> {\n    return apiClient.get<NewsItem[]>(`/conferences/${conferenceId}/news`, params);\n  }\n};\n"], "names": [], "mappings": ";;;AAAA;;AA8CO,MAAM,oBAAoB;IAC/B,sBAAsB;IACtB,MAAM;QACJ,OAAO,oHAAA,CAAA,YAAS,CAAC,GAAG,CAAe;IACrC;IAEA,gCAAgC;IAChC,MAAM;QACJ,OAAO,oHAAA,CAAA,YAAS,CAAC,GAAG,CAAa;IACnC;IAEA,gCAAgC;IAChC,MAAM,eAAc,EAAU;QAC5B,OAAO,oHAAA,CAAA,YAAS,CAAC,GAAG,CAAa,CAAC,aAAa,EAAE,IAAI;IACvD;IAEA,0BAA0B;IAC1B,MAAM,uBAAsB,YAAoB;QAC9C,OAAO,oHAAA,CAAA,YAAS,CAAC,GAAG,CAAY,CAAC,aAAa,EAAE,aAAa,SAAS,CAAC;IACzE;IAEA,sBAAsB;IACtB,MAAM,mBAAkB,YAAoB,EAAE,MAG7C;QACC,OAAO,oHAAA,CAAA,YAAS,CAAC,GAAG,CAAa,CAAC,aAAa,EAAE,aAAa,KAAK,CAAC,EAAE;IACxE;AACF", "debugId": null}}, {"offset": {"line": 817, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya/lipya1/src/components/ApiStatus.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { conferenceService } from '../services/conferences';\nimport { handleApiError } from '../lib/api';\n\nconst ApiStatus: React.FC = () => {\n  const [status, setStatus] = useState<'checking' | 'connected' | 'error'>('checking');\n  const [message, setMessage] = useState('Checking API connection...');\n  const [conferenceData, setConferenceData] = useState<any>(null);\n\n  useEffect(() => {\n    const checkApiConnection = async () => {\n      try {\n        setStatus('checking');\n        setMessage('Connecting to API...');\n\n        // Test API connection by fetching current conference\n        const response = await conferenceService.getCurrentConference();\n        \n        if (response.success && response.data) {\n          setStatus('connected');\n          setMessage('API connected successfully!');\n          setConferenceData(response.data);\n        } else {\n          setStatus('error');\n          setMessage(response.message || 'No current conference found');\n        }\n      } catch (error: any) {\n        setStatus('error');\n        setMessage(handleApiError(error));\n      }\n    };\n\n    checkApiConnection();\n  }, []);\n\n  const getStatusColor = () => {\n    switch (status) {\n      case 'checking':\n        return 'bg-yellow-100 border-yellow-400 text-yellow-800';\n      case 'connected':\n        return 'bg-green-100 border-green-400 text-green-800';\n      case 'error':\n        return 'bg-red-100 border-red-400 text-red-800';\n      default:\n        return 'bg-gray-100 border-gray-400 text-gray-800';\n    }\n  };\n\n  const getStatusIcon = () => {\n    switch (status) {\n      case 'checking':\n        return '⏳';\n      case 'connected':\n        return '✅';\n      case 'error':\n        return '❌';\n      default:\n        return '❓';\n    }\n  };\n\n  return (\n    <div className=\"fixed bottom-4 right-4 z-50\">\n      <div className={`p-3 rounded-lg border-2 shadow-lg max-w-sm ${getStatusColor()}`}>\n        <div className=\"flex items-center gap-2 mb-2\">\n          <span className=\"text-lg\">{getStatusIcon()}</span>\n          <span className=\"font-semibold\">API Status</span>\n        </div>\n        <p className=\"text-sm\">{message}</p>\n        \n        {conferenceData && (\n          <div className=\"mt-2 text-xs\">\n            <p><strong>Conference:</strong> {conferenceData.title}</p>\n            <p><strong>Location:</strong> {conferenceData.location}</p>\n            <p><strong>Date:</strong> {new Date(conferenceData.start_date).toLocaleDateString()}</p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default ApiStatus;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,MAAM,YAAsB;;IAC1B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsC;IACzE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAE1D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM;0DAAqB;oBACzB,IAAI;wBACF,UAAU;wBACV,WAAW;wBAEX,qDAAqD;wBACrD,MAAM,WAAW,MAAM,iIAAA,CAAA,oBAAiB,CAAC,oBAAoB;wBAE7D,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;4BACrC,UAAU;4BACV,WAAW;4BACX,kBAAkB,SAAS,IAAI;wBACjC,OAAO;4BACL,UAAU;4BACV,WAAW,SAAS,OAAO,IAAI;wBACjC;oBACF,EAAE,OAAO,OAAY;wBACnB,UAAU;wBACV,WAAW,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE;oBAC5B;gBACF;;YAEA;QACF;8BAAG,EAAE;IAEL,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAW,CAAC,2CAA2C,EAAE,kBAAkB;;8BAC9E,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAU;sCAAW;;;;;;sCAC3B,6LAAC;4BAAK,WAAU;sCAAgB;;;;;;;;;;;;8BAElC,6LAAC;oBAAE,WAAU;8BAAW;;;;;;gBAEvB,gCACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CAAE,6LAAC;8CAAO;;;;;;gCAAoB;gCAAE,eAAe,KAAK;;;;;;;sCACrD,6LAAC;;8CAAE,6LAAC;8CAAO;;;;;;gCAAkB;gCAAE,eAAe,QAAQ;;;;;;;sCACtD,6LAAC;;8CAAE,6LAAC;8CAAO;;;;;;gCAAc;gCAAE,IAAI,KAAK,eAAe,UAAU,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;AAM7F;GA5EM;KAAA;uCA8ES", "debugId": null}}]}