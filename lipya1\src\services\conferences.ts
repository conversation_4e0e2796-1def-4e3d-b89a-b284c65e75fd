import { apiClient, ApiResponse } from '../lib/api';

export interface Conference {
  id: number;
  title: string;
  subtitle: string;
  description: string;
  start_date: string;
  end_date: string;
  location: string;
  venue: string;
  logo_url: string;
  banner_image_url: string;
  registration_fee_local: number;
  registration_fee_international: number;
  currency_local: string;
  currency_international: string;
  is_active: boolean;
  is_current: boolean;
  featured_speakers?: Speaker[];
  recent_news?: NewsItem[];
}

export interface Speaker {
  id: number;
  name: string;
  title: string;
  bio: string;
  image_url: string;
  is_keynote: boolean;
  is_committee: boolean;
  is_featured: boolean;
  order: number;
}

export interface NewsItem {
  id: number;
  title: string;
  slug: string;
  excerpt: string;
  featured_image_url: string;
  published_at: string;
  reading_time: string;
  is_featured: boolean;
}

export const conferenceService = {
  // Get all conferences
  async getConferences(): Promise<ApiResponse<Conference[]>> {
    return apiClient.get<Conference[]>('/conferences');
  },

  // Get current active conference
  async getCurrentConference(): Promise<ApiResponse<Conference>> {
    return apiClient.get<Conference>('/conferences/current');
  },

  // Get specific conference by ID
  async getConference(id: number): Promise<ApiResponse<Conference>> {
    return apiClient.get<Conference>(`/conferences/${id}`);
  },

  // Get conference speakers
  async getConferenceSpeakers(conferenceId: number): Promise<ApiResponse<Speaker[]>> {
    return apiClient.get<Speaker[]>(`/conferences/${conferenceId}/speakers`);
  },

  // Get conference news
  async getConferenceNews(conferenceId: number, params?: {
    per_page?: number;
    page?: number;
  }): Promise<ApiResponse<NewsItem[]>> {
    return apiClient.get<NewsItem[]>(`/conferences/${conferenceId}/news`, params);
  }
};
