RewriteEngine On

# Handle API requests
RewriteCond %{REQUEST_URI} ^/api/v1/
RewriteRule ^api/v1/(.*)$ simple-api.php [QSA,L]

# Handle admin panel (redirect to simple admin)
RewriteCond %{REQUEST_URI} ^/admin
RewriteRule ^admin/?(.*)$ admin.php [QSA,L]

# Default Laravel routing (if <PERSON><PERSON> is available)
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^ index.php [L]
