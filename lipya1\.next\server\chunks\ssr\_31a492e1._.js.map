{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya/lipya1/src/app/call-for-papers/page.tsx"], "sourcesContent": ["// components/About.tsx\r\nimport React from 'react';\r\nimport {\r\n  FaSun,\r\n  FaLeaf,\r\n  FaSeedling,\r\n  FaOilCan,\r\n  FaBalanceScale,\r\n  FaGlobeAmericas,\r\n  FaMapMarkedAlt,\r\n  FaRobot,\r\n} from 'react-icons/fa';\r\n\r\nexport default function About() {\r\n  return (\r\n    <div>\r\n      {/* صورة الهيرو */}\r\n      <div className=\"relative h-[300px] md:h-[400px] w-full\">\r\n        <img\r\n          src=\"/photo1.png\"\r\n          alt=\"About IREGO\"\r\n          className=\"object-cover w-full h-full\"\r\n        />\r\n        <div className=\"absolute inset-0 bg-black/50 flex items-center justify-center\">\r\n          <h1 className=\"text-3xl md:text-5xl font-bold text-white\">\r\n            Call For <span className=\"text-orange-500\">Paper</span>\r\n          </h1>\r\n        </div>\r\n      </div>\r\n\r\n      {/* مقدمة المؤتمر */}\r\n      <div className=\"mt-10 text-center px-4 max-w-3xl mx-auto\">\r\n        <h2 className=\"text-lg md:text-2xl font-semibold\">\r\n          International Renewable Energy, Gas & Oil{' '}\r\n          <span className=\"text-orange-500 uppercase font-bold\">\r\n            and Climate Change Conference\r\n          </span>\r\n        </h2>\r\n        <p className=\"text-gray-700 text-sm md:text-base mb-8 max-w-2xl\">\r\n          We invite scholars, researchers, professionals, and policymakers to\r\n          submit papers for the 1st International Conference on Renewable\r\n          Energy, Gas & Oil, and Climate Change. The event will explore the\r\n          critical role of renewable energy in tackling climate change,\r\n          reducing emissions, and advancing sustainable development through\r\n          clean energy solutions like solar, wind, hydro, and geothermal.\r\n        </p>\r\n      </div>\r\n\r\n      {/* العناوين */}\r\n      <div className=\"mt-10 text-center px-4 max-w-3xl mx-auto\">\r\n        <h2 className=\"text-lg md:text-3xl font-semibold\">\r\n          Topics of{' '}\r\n          <span className=\"text-orange-500 uppercase font-bold\">Interest</span>\r\n        </h2>\r\n\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 p-4 mt-6\">\r\n          {/* كل كارت */}\r\n          <div className=\"bg-white rounded-2xl shadow-md p-4 text-center\">\r\n            <FaSun className=\"text-orange-500 text-4xl mx-auto mb-2\" />\r\n            <h2 className=\"text-orange-500 text-lg font-bold mb-3\">\r\n              Renewable Energy Technologies\r\n            </h2>\r\n            <ul className=\"text-gray-700 space-y-1 text-sm\">\r\n              <li>Solar</li>\r\n              <li>WIND</li>\r\n              <li>Hydro</li>\r\n              <li>Geothermal</li>\r\n              <li>Green hydrogen</li>\r\n              <li>Efficiency</li>\r\n              <li>Smart grids</li>\r\n              <li>Biomass</li>\r\n            </ul>\r\n          </div>\r\n\r\n          <div className=\"bg-white rounded-2xl shadow-md p-4 text-center\">\r\n            <FaLeaf className=\"text-orange-500 text-4xl mx-auto mb-2\" />\r\n            <h2 className=\"text-orange-500 text-lg font-bold mb-3\">\r\n              Climate Change Mitigation\r\n            </h2>\r\n            <ul className=\"text-gray-700 space-y-1 text-sm\">\r\n              <li>Role of renewable energy</li>\r\n              <li>Net-zero emissions</li>\r\n              <li>Carbon capture</li>\r\n              <li>Climate resilience</li>\r\n            </ul>\r\n          </div>\r\n\r\n          <div className=\"bg-white rounded-2xl shadow-md p-4 text-center\">\r\n            <FaSeedling className=\"text-orange-500 text-4xl mx-auto mb-2\" />\r\n            <h2 className=\"text-orange-500 text-lg font-bold mb-3\">\r\n              Sustainable Development\r\n            </h2>\r\n            <ul className=\"text-gray-700 space-y-1 text-sm\">\r\n              <li>Socio-economic impacts</li>\r\n              <li>SDGs</li>\r\n              <li>Energy access</li>\r\n              <li>Community development</li>\r\n            </ul>\r\n          </div>\r\n\r\n          <div className=\"bg-white rounded-2xl shadow-md p-4 text-center\">\r\n            <FaOilCan className=\"text-orange-500 text-4xl mx-auto mb-2\" />\r\n            <h2 className=\"text-orange-500 text-lg font-bold mb-3\">\r\n              Oil and Gas Industry Transformation\r\n            </h2>\r\n            <ul className=\"text-gray-700 space-y-1 text-sm\">\r\n              <li>Transition to clean fuels</li>\r\n              <li>Circular economy</li>\r\n              <li>Digital transformation</li>\r\n              <li>Sustainable extraction</li>\r\n            </ul>\r\n          </div>\r\n\r\n          <div className=\"bg-white rounded-2xl shadow-md p-4 text-center\">\r\n            <FaBalanceScale className=\"text-orange-500 text-4xl mx-auto mb-2\" />\r\n            <h2 className=\"text-orange-500 text-lg font-bold mb-3\">\r\n              Policy, Economics, Regulation\r\n            </h2>\r\n            <ul className=\"text-gray-700 space-y-1 text-sm\">\r\n              <li>Policies</li>\r\n              <li>Cooperation</li>\r\n              <li>Financing</li>\r\n              <li>Market analysis</li>\r\n              <li>Investment opportunities</li>\r\n            </ul>\r\n          </div>\r\n\r\n          <div className=\"bg-white rounded-2xl shadow-md p-4 text-center\">\r\n            <FaGlobeAmericas className=\"text-orange-500 text-4xl mx-auto mb-2\" />\r\n            <h2 className=\"text-orange-500 text-lg font-bold mb-3\">\r\n              Environmental & Social Impacts\r\n            </h2>\r\n            <ul className=\"text-gray-700 space-y-1 text-sm\">\r\n              <li>Biodiversity</li>\r\n              <li>Public perception</li>\r\n              <li>Environmental assessment</li>\r\n              <li>Social impact</li>\r\n            </ul>\r\n          </div>\r\n\r\n          <div className=\"bg-white rounded-2xl shadow-md p-4 text-center\">\r\n            <FaMapMarkedAlt className=\"text-orange-500 text-4xl mx-auto mb-2\" />\r\n            <h2 className=\"text-orange-500 text-lg font-bold mb-3\">\r\n              Applications of GIS & Remote Sensing\r\n            </h2>\r\n            <ul className=\"text-gray-700 space-y-1 text-sm\">\r\n              <li>Monitoring</li>\r\n              <li>Spatial data analysis</li>\r\n              <li>Compliance</li>\r\n              <li>Resource mapping</li>\r\n            </ul>\r\n          </div>\r\n\r\n          <div className=\"bg-white rounded-2xl shadow-md p-4 text-center\">\r\n            <FaRobot className=\"text-orange-500 text-4xl mx-auto mb-2\" />\r\n            <h2 className=\"text-orange-500 text-lg font-bold mb-3\">\r\n              AI in Renewable Energy & Climate\r\n            </h2>\r\n            <ul className=\"text-gray-700 space-y-1 text-sm\">\r\n              <li>Forecasting</li>\r\n              <li>ML models</li>\r\n              <li>Monitoring</li>\r\n              <li>Predictive analytics</li>\r\n            </ul>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* الإرشادات */}\r\n      <div className=\"mt-10 text-center px-4 max-w-3xl mx-auto\">\r\n        <h2 className=\"text-lg md:text-3xl font-semibold\">\r\n          Submission{' '}\r\n          <span className=\"text-orange-500 uppercase font-bold\">\r\n            Guidelines\r\n          </span>\r\n        </h2>\r\n        <p className=\"text-gray-700 text-sm md:text-base mb-8 max-w-2xl\">\r\n          We invite researchers, practitioners, and industry experts to submit\r\n          original papers that contribute to the advancement of renewable\r\n          energy technologies and sustainable development. All submissions will\r\n          undergo a rigorous peer-review process to ensure academic excellence\r\n          and relevance to the conference themes.\r\n        </p>\r\n      </div>\r\n\r\n      {/* ملاحظة 1 */}\r\n      <div className=\"bg-orange-100 text-black border border-orange-300 rounded-xl p-4 my-6 text-center font-medium\">\r\n        Important: Papers must be original work not previously published or\r\n        under review elsewhere.\r\n      </div>\r\n\r\n      {/* التنسيق */}\r\n      <div className=\"mt-10 text-center px-4 max-w-3xl mx-auto\">\r\n        <h2 className=\"text-lg md:text-3xl font-semibold\">\r\n          Paper{' '}\r\n          <span className=\"text-orange-500 uppercase font-bold\">Format</span>\r\n        </h2>\r\n        <p className=\"text-gray-700 text-sm md:text-base mb-8 max-w-2xl\">\r\n          Papers should be submitted in IEEE format, not exceeding 6 pages.\r\n          Authors are required to follow the IEEE formatting guidelines for\r\n          conference papers.\r\n        </p>\r\n      </div>\r\n\r\n      {/* ملاحظة 2 */}\r\n      <div className=\"bg-orange-100 text-black border border-orange-300 rounded-xl p-4 my-6 text-center font-medium\">\r\n        Format Requirements: IEEE Xplore compatible format\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,uBAAuB;;;;;AAEvB;;;AAWe,SAAS;IACtB,qBACE,8OAAC;;0BAEC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,KAAI;wBACJ,KAAI;wBACJ,WAAU;;;;;;kCAEZ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;;gCAA4C;8CAC/C,8OAAC;oCAAK,WAAU;8CAAkB;;;;;;;;;;;;;;;;;;;;;;;0BAMjD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;4BAAoC;4BACN;0CAC1C,8OAAC;gCAAK,WAAU;0CAAsC;;;;;;;;;;;;kCAIxD,8OAAC;wBAAE,WAAU;kCAAoD;;;;;;;;;;;;0BAWnE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;4BAAoC;4BACtC;0CACV,8OAAC;gCAAK,WAAU;0CAAsC;;;;;;;;;;;;kCAGxD,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8IAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDAGvD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAIR,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8IAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDAGvD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAIR,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8IAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDAGvD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAIR,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8IAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDAGvD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAIR,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8IAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;kDAC1B,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDAGvD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAIR,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8IAAA,CAAA,kBAAe;wCAAC,WAAU;;;;;;kDAC3B,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDAGvD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAIR,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8IAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;kDAC1B,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDAGvD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAIR,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8IAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDAGvD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;4BAAoC;4BACrC;0CACX,8OAAC;gCAAK,WAAU;0CAAsC;;;;;;;;;;;;kCAIxD,8OAAC;wBAAE,WAAU;kCAAoD;;;;;;;;;;;;0BAUnE,8OAAC;gBAAI,WAAU;0BAAgG;;;;;;0BAM/G,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;4BAAoC;4BAC1C;0CACN,8OAAC;gCAAK,WAAU;0CAAsC;;;;;;;;;;;;kCAExD,8OAAC;wBAAE,WAAU;kCAAoD;;;;;;;;;;;;0BAQnE,8OAAC;gBAAI,WAAU;0BAAgG;;;;;;;;;;;;AAKrH", "debugId": null}}, {"offset": {"line": 792, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya/lipya1/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 830, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya/lipya1/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;AAAqC,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;AAEgG,EAAC;IA2BrHiB,UAAU;;;;;;;;;AAlBZ,OAAO,MAAMd,eAAe,6CAAA;IAC1BC,MAAAA,GAASC;IACTC,EAAAA,OAAAA;IAAAA,CAAWC;IAAAA;QACb,EAAC,UAAA;YAAA;YAAA;gBAED,YAAA;oBAAA,CAAc;oBAAA,+BAA0C;qBAAE,wBAAwB;wBAAuB,UAAA,CAAA;wBAAA,QAAA;4BAAA,IAAA;4BAAA;yBAAA;oBAEzG;iBAAA,0DAA4D;YAC5D;YAAA,IAAO,MAAMC,cAAc,IAAIX,mBAAmB;kBAChDY,QAAAA,CAAAA,CAAY;YAAA;SAAA;;SACVC,MAAMZ,UAAUa,QAAQ;cACxBC,IAAAA;YAAAA,CAAM,GAAA;YAAA;SAAA;cACNC,OAAAA;YAAAA,EAAU,EAAA;YAAA;SAAA;cACV,OAAA;YAAA,IAAA,6BAA2C;YAAA;SAAA;cAC3CC,UAAAA;YAAAA,CAAY,GAAA;YAAA;SAAA;;OACZC,UAAU;QACVC,MAAAA;IAAAA,GAAU,EAAE;CAAA;;;AAKhB,GAAE,GAAA,uBAAA,sBAAA,CAAA", "ignoreList": [0], "debugId": null}}]}