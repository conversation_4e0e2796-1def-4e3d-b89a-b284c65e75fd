import { apiClient, ApiResponse } from '../lib/api';

export interface NewsItem {
  id: number;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  featured_image_url: string;
  author_name: string;
  published_at: string;
  reading_time: string;
  is_featured: boolean;
  conference_id: number;
  conference_title?: string;
  tags?: string[];
}

export interface NewsFilters {
  conference_id?: number;
  featured?: boolean;
  per_page?: number;
  page?: number;
}

export const newsService = {
  // Get all news
  async getNews(filters?: NewsFilters): Promise<ApiResponse<NewsItem[]>> {
    return apiClient.get<NewsItem[]>('/news', filters);
  },

  // Get specific news by slug
  async getNewsItem(slug: string): Promise<ApiResponse<NewsItem>> {
    return apiClient.get<NewsItem>(`/news/${slug}`);
  },

  // Get featured news
  async getFeaturedNews(): Promise<ApiResponse<NewsItem[]>> {
    return apiClient.get<NewsItem[]>('/news/featured');
  },

  // Get recent news
  async getRecentNews(): Promise<ApiResponse<NewsItem[]>> {
    return apiClient.get<NewsItem[]>('/news/recent');
  }
};
