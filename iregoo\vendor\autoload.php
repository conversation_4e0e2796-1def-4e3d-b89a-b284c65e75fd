<?php

// Temporary autoload file for development
// This is a minimal autoload to get <PERSON><PERSON> running without full composer install

// Define base path
define('LARAVEL_START', microtime(true));

// Register the autoloader
spl_autoload_register(function ($class) {
    // Convert namespace to file path
    $file = __DIR__ . '/../app/' . str_replace(['App\\', '\\'], ['', '/'], $class) . '.php';
    
    if (file_exists($file)) {
        require_once $file;
        return true;
    }
    
    // Try database classes
    $file = __DIR__ . '/../database/' . str_replace(['Database\\', '\\'], ['', '/'], $class) . '.php';
    
    if (file_exists($file)) {
        require_once $file;
        return true;
    }
    
    return false;
});

// Basic Laravel classes that we need
if (!class_exists('Illuminate\Foundation\Application')) {
    echo "Laravel framework not installed. Please run 'composer install' first.\n";
    exit(1);
}

// Return a basic autoloader
return new class {
    public function loadClass($class) {
        return false;
    }
};
