# Frontend API Integration

This document describes how the Next.js frontend is connected to the Laravel backend API.

## Overview

The frontend now connects to the Laravel backend API to fetch real data instead of using mock data. The integration includes:

- Authentication (login/register/logout)
- Conference data
- Speaker information
- News articles
- Registration system
- Sponsors and events

## Configuration

### Environment Variables

Create a `.env.local` file in the root directory:

```env
NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### API Base URL

The API base URL is configured in `src/lib/api.ts` and reads from the environment variable `NEXT_PUBLIC_API_URL`.

## File Structure

```
src/
├── lib/
│   └── api.ts                 # API client configuration
├── services/
│   ├── auth.ts               # Authentication services
│   ├── conferences.ts        # Conference API calls
│   ├── speakers.ts           # Speaker API calls
│   ├── registration.ts       # Registration API calls
│   ├── news.ts              # News API calls
│   ├── sponsors.ts          # Sponsor API calls
│   └── events.ts            # Event API calls
├── hooks/
│   └── useConference.ts     # Conference data hook
├── components/
│   ├── ApiStatus.tsx        # API connection status
│   └── RegistrationForm.tsx # Registration form component
└── context/
    └── UserContext.tsx      # Updated user context with real auth
```

## API Services

### Authentication Service (`services/auth.ts`)

- `login(credentials)` - User login
- `register(userData)` - User registration
- `logout()` - User logout
- `me()` - Get current user
- `refresh()` - Refresh JWT token

### Conference Service (`services/conferences.ts`)

- `getConferences()` - Get all conferences
- `getCurrentConference()` - Get current active conference
- `getConference(id)` - Get specific conference

### Speaker Service (`services/speakers.ts`)

- `getSpeakers(filters)` - Get all speakers
- `getSpeaker(id)` - Get specific speaker
- `getCommittees()` - Get committee members
- `getKeynotes()` - Get keynote speakers

### Registration Service (`services/registration.ts`)

- `submitRegistration(data)` - Submit new registration
- `getRegistration(code)` - Get registration by confirmation code
- `checkStatus(data)` - Check registration status

### News Service (`services/news.ts`)

- `getNews(filters)` - Get all news
- `getNewsItem(slug)` - Get specific news article
- `getFeaturedNews()` - Get featured news
- `getRecentNews()` - Get recent news

## Updated Components

### UserContext (`context/UserContext.tsx`)

Now includes real authentication methods:
- `login(email, password)` - Authenticate user
- `register(userData)` - Register new user
- `logout()` - Sign out user
- `isAuthenticated` - Authentication status
- `isLoading` - Loading state

### Login Page (`app/login/page.tsx`)

- Connected to real authentication API
- Shows loading states
- Handles API errors
- Redirects on successful login

### Signup Page (`app/signup/page.tsx`)

- Connected to real registration API
- Form validation
- Password confirmation
- Error handling

### Registration Page (`app/registration/page.tsx`)

- Added registration form component
- Connects to conference registration API
- Shows current conference information
- Handles form submission and validation

### Navbar (`components/Navbar.tsx`)

- Uses real authentication state
- Proper logout functionality
- Shows user information when logged in

## New Components

### RegistrationForm (`components/RegistrationForm.tsx`)

A complete registration form that:
- Fetches current conference data
- Validates form inputs
- Submits registration to API
- Shows success/error messages
- Handles different participant types and fees

### ApiStatus (`components/ApiStatus.tsx`)

A status indicator that:
- Tests API connection
- Shows connection status
- Displays current conference info
- Provides visual feedback

## Testing

### API Test Page

Visit `/api-test` to test all API endpoints:
- Tests each service individually
- Shows success/error status
- Displays detailed results
- Useful for debugging API issues

### Manual Testing

1. Start the Laravel backend: `php artisan serve`
2. Start the Next.js frontend: `npm run dev`
3. Check the API status indicator in the bottom-right corner
4. Test authentication by logging in/registering
5. Test registration form functionality
6. Visit `/api-test` for comprehensive API testing

## Error Handling

The integration includes comprehensive error handling:
- API client handles HTTP errors
- Services provide user-friendly error messages
- Components show loading states
- Fallback UI for failed API calls

## Authentication Flow

1. User enters credentials
2. Frontend calls Laravel API
3. Laravel returns JWT token
4. Token stored in localStorage
5. Token included in subsequent API calls
6. Automatic token refresh when needed
7. Clear auth data on logout

## Data Flow

1. Components use service functions
2. Services call API client
3. API client handles HTTP requests
4. Responses processed and returned
5. Components update UI with data
6. Error states handled gracefully

## Next Steps

To complete the integration:

1. **Backend Setup**: Ensure Laravel backend is running with proper CORS configuration
2. **Database**: Make sure database is seeded with sample data
3. **Testing**: Test all functionality thoroughly
4. **Production**: Update environment variables for production deployment
5. **Optimization**: Add caching and performance optimizations as needed

## Troubleshooting

### Common Issues

1. **CORS Errors**: Ensure Laravel CORS is configured for frontend domain
2. **API Not Found**: Check that Laravel backend is running on correct port
3. **Authentication Issues**: Verify JWT configuration in Laravel
4. **Network Errors**: Check API URL in environment variables

### Debug Steps

1. Check API status indicator
2. Open browser developer tools
3. Check network tab for API calls
4. Verify environment variables
5. Test API endpoints directly
6. Check Laravel logs for errors
