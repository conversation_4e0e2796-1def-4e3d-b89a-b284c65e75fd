'use client';

import React, { useState } from 'react';
import { conferenceService } from '../../services/conferences';
import { speakerService } from '../../services/speakers';
import { newsService } from '../../services/news';
import { sponsorService } from '../../services/sponsors';
import { eventService } from '../../services/events';
import { handleApiError } from '../../lib/api';

const ApiTestPage: React.FC = () => {
  const [results, setResults] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState<Record<string, boolean>>({});

  const testEndpoint = async (name: string, testFunction: () => Promise<any>) => {
    setLoading(prev => ({ ...prev, [name]: true }));
    
    try {
      const result = await testFunction();
      setResults(prev => ({ 
        ...prev, 
        [name]: { 
          success: true, 
          data: result.data,
          message: result.message || 'Success'
        }
      }));
    } catch (error: any) {
      setResults(prev => ({ 
        ...prev, 
        [name]: { 
          success: false, 
          error: handleApiError(error),
          details: error
        }
      }));
    } finally {
      setLoading(prev => ({ ...prev, [name]: false }));
    }
  };

  const tests = [
    {
      name: 'Current Conference',
      test: () => conferenceService.getCurrentConference()
    },
    {
      name: 'All Conferences',
      test: () => conferenceService.getConferences()
    },
    {
      name: 'Speakers',
      test: () => speakerService.getSpeakers()
    },
    {
      name: 'Keynote Speakers',
      test: () => speakerService.getKeynotes()
    },
    {
      name: 'Committee Members',
      test: () => speakerService.getCommittees()
    },
    {
      name: 'News',
      test: () => newsService.getNews()
    },
    {
      name: 'Featured News',
      test: () => newsService.getFeaturedNews()
    },
    {
      name: 'Recent News',
      test: () => newsService.getRecentNews()
    },
    {
      name: 'Sponsors',
      test: () => sponsorService.getSponsors()
    },
    {
      name: 'Events',
      test: () => eventService.getEvents()
    },
    {
      name: 'Schedule',
      test: () => eventService.getSchedule()
    }
  ];

  const runAllTests = async () => {
    for (const test of tests) {
      await testEndpoint(test.name, test.test);
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 mt-20">
      <h1 className="text-3xl font-bold mb-8 text-center">API Connection Test</h1>
      
      <div className="mb-6 text-center">
        <button
          onClick={runAllTests}
          className="bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg font-semibold"
        >
          Run All Tests
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {tests.map((test) => (
          <div key={test.name} className="border rounded-lg p-4 shadow-md">
            <div className="flex justify-between items-center mb-3">
              <h3 className="font-semibold">{test.name}</h3>
              <button
                onClick={() => testEndpoint(test.name, test.test)}
                disabled={loading[test.name]}
                className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white px-3 py-1 rounded text-sm"
              >
                {loading[test.name] ? 'Testing...' : 'Test'}
              </button>
            </div>
            
            {results[test.name] && (
              <div className={`p-3 rounded text-sm ${
                results[test.name].success 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {results[test.name].success ? (
                  <div>
                    <p className="font-semibold">✅ Success</p>
                    <p className="text-xs mt-1">
                      {Array.isArray(results[test.name].data) 
                        ? `${results[test.name].data.length} items found`
                        : 'Data received'
                      }
                    </p>
                  </div>
                ) : (
                  <div>
                    <p className="font-semibold">❌ Error</p>
                    <p className="text-xs mt-1">{results[test.name].error}</p>
                  </div>
                )}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Display detailed results */}
      <div className="mt-8">
        <h2 className="text-2xl font-bold mb-4">Detailed Results</h2>
        <div className="space-y-4">
          {Object.entries(results).map(([name, result]) => (
            <div key={name} className="border rounded-lg p-4">
              <h3 className="font-semibold mb-2">{name}</h3>
              <pre className="bg-gray-100 p-3 rounded text-xs overflow-auto max-h-40">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ApiTestPage;
