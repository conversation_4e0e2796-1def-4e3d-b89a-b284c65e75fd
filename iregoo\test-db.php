<?php
/**
 * Database Test and Initialization
 */

$dbFile = __DIR__ . '/database/simple.db';

echo "<h2>Database Test</h2>";

// Check if database directory exists
if (!is_dir(__DIR__ . '/database')) {
    mkdir(__DIR__ . '/database', 0755, true);
    echo "<p>✅ Created database directory</p>";
}

// Initialize database
$pdo = new PDO('sqlite:' . $dbFile);

// Create tables
$pdo->exec("
    CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        role TEXT DEFAULT 'user',
        is_active INTEGER DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
");

$pdo->exec("
    CREATE TABLE IF NOT EXISTS conferences (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        subtitle TEXT,
        description TEXT,
        start_date DATE,
        end_date DATE,
        location TEXT,
        venue TEXT,
        registration_fee_local DECIMAL(10,2),
        registration_fee_international DECIMAL(10,2),
        currency_local TEXT DEFAULT 'LYD',
        currency_international TEXT DEFAULT 'EUR',
        is_active INTEGER DEFAULT 1,
        is_current INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
");

$pdo->exec("
    CREATE TABLE IF NOT EXISTS registrations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        conference_id INTEGER,
        full_name TEXT NOT NULL,
        email TEXT NOT NULL,
        phone TEXT,
        country TEXT,
        organization TEXT,
        position TEXT,
        participant_type TEXT DEFAULT 'local',
        registration_type TEXT DEFAULT 'individual',
        dietary_requirements TEXT,
        special_needs TEXT,
        payment_method TEXT DEFAULT 'cash',
        registration_fee DECIMAL(10,2),
        currency TEXT,
        payment_status TEXT DEFAULT 'pending',
        confirmation_code TEXT UNIQUE,
        is_confirmed INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
");

echo "<p>✅ Tables created successfully</p>";

// Insert admin user
$adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
$stmt = $pdo->prepare("INSERT OR IGNORE INTO users (name, email, password, role) VALUES (?, ?, ?, ?)");
$stmt->execute(['Super Admin', '<EMAIL>', $adminPassword, 'admin']);

echo "<p>✅ Admin user created</p>";

// Insert sample conference
$stmt = $pdo->prepare("INSERT OR IGNORE INTO conferences (title, subtitle, description, start_date, end_date, location, venue, registration_fee_local, registration_fee_international, is_active, is_current) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
$stmt->execute([
    'International Renewable Energy, Gas & Oil, and Climate Change Conference',
    'IREGO Conference 2025',
    'A pioneering conference bringing together experts in renewable energy, oil & gas, and climate change solutions.',
    '2025-11-25',
    '2025-11-27',
    'Tripoli, Libya',
    'Conference Center Tripoli',
    200.00,
    200.00,
    1,
    1
]);

echo "<p>✅ Sample conference created</p>";

// Test admin login
$stmt = $pdo->prepare("SELECT * FROM users WHERE email = ? AND role = 'admin'");
$stmt->execute(['<EMAIL>']);
$admin = $stmt->fetch(PDO::FETCH_ASSOC);

if ($admin) {
    echo "<p>✅ Admin user found: " . htmlspecialchars($admin['name']) . " (" . htmlspecialchars($admin['email']) . ")</p>";
    
    if (password_verify('admin123', $admin['password'])) {
        echo "<p>✅ Password verification successful</p>";
    } else {
        echo "<p>❌ Password verification failed</p>";
    }
} else {
    echo "<p>❌ Admin user not found</p>";
}

// Show all users
$stmt = $pdo->query("SELECT * FROM users");
$users = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<h3>All Users:</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Role</th><th>Active</th></tr>";
foreach ($users as $user) {
    echo "<tr>";
    echo "<td>" . htmlspecialchars($user['id']) . "</td>";
    echo "<td>" . htmlspecialchars($user['name']) . "</td>";
    echo "<td>" . htmlspecialchars($user['email']) . "</td>";
    echo "<td>" . htmlspecialchars($user['role']) . "</td>";
    echo "<td>" . ($user['is_active'] ? 'Yes' : 'No') . "</td>";
    echo "</tr>";
}
echo "</table>";

// Show all conferences
$stmt = $pdo->query("SELECT * FROM conferences");
$conferences = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<h3>All Conferences:</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>ID</th><th>Title</th><th>Location</th><th>Start Date</th><th>Active</th><th>Current</th></tr>";
foreach ($conferences as $conf) {
    echo "<tr>";
    echo "<td>" . htmlspecialchars($conf['id']) . "</td>";
    echo "<td>" . htmlspecialchars($conf['title']) . "</td>";
    echo "<td>" . htmlspecialchars($conf['location']) . "</td>";
    echo "<td>" . htmlspecialchars($conf['start_date']) . "</td>";
    echo "<td>" . ($conf['is_active'] ? 'Yes' : 'No') . "</td>";
    echo "<td>" . ($conf['is_current'] ? 'Yes' : 'No') . "</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h3>Database File Info:</h3>";
echo "<p>Database file: " . $dbFile . "</p>";
echo "<p>File exists: " . (file_exists($dbFile) ? 'Yes' : 'No') . "</p>";
echo "<p>File size: " . (file_exists($dbFile) ? filesize($dbFile) . ' bytes' : 'N/A') . "</p>";
echo "<p>File permissions: " . (file_exists($dbFile) ? substr(sprintf('%o', fileperms($dbFile)), -4) : 'N/A') . "</p>";

echo "<hr>";
echo "<p><a href='/admin'>Go to Admin Panel</a></p>";
echo "<p><a href='/'>Go to Home</a></p>";
?>
