# IREGO Conference Backend - Quick Start

## 🚀 الباك إند جاهز للاستخدام!

تم إنشاء نظام باك إند مبسط يعمل بدون Laravel dependencies معقدة ويوفر:

### ✅ المميزات المتاحة:

1. **API كامل للفرونت إند**
   - تسجيل الدخول والتسجيل
   - إدارة المؤتمرات
   - تسجيل المشاركين
   - CORS مفعل للفرونت إند

2. **لوحة إدمن كاملة**
   - Dashboard مع إحصائيات
   - إدارة التسجيلات
   - معلومات API
   - واجهة سهلة الاستخدام

3. **قاعدة بيانات SQLite**
   - لا تحتاج إعداد MySQL
   - بيانات تجريبية جاهزة
   - سهولة في النقل والنسخ

## 🎯 الروابط المهمة:

- **الصفحة الرئيسية:** http://localhost:8000
- **لوحة الإدمن:** http://localhost:8000/admin
- **API Base URL:** http://localhost:8000/api/v1

## 🔐 بيانات الدخول:

- **Email:** <EMAIL>
- **Password:** admin123

## 📡 API Endpoints الجاهزة:

### Authentication
- `POST /api/v1/auth/login` - تسجيل الدخول
- `POST /api/v1/auth/register` - تسجيل مستخدم جديد

### Conferences
- `GET /api/v1/conferences/current` - المؤتمر الحالي
- `GET /api/v1/conferences` - جميع المؤتمرات

### Registrations
- `POST /api/v1/registrations` - تسجيل في المؤتمر

## 🔧 كيفية التشغيل:

### 1. تشغيل الباك إند:
```bash
cd iregoo
php -S localhost:8000
```

### 2. تشغيل الفرونت إند:
```bash
cd lipya1
npm run dev
```

## 🧪 اختبار الاتصال:

1. افتح http://localhost:8000 للتأكد من عمل الباك إند
2. افتح http://localhost:3000 للفرونت إند
3. تحقق من API Status في أسفل يمين الفرونت إند
4. جرب تسجيل الدخول والتسجيل في المؤتمر

## 📊 البيانات التجريبية:

- مؤتمر واحد: "IREGO Conference 2025"
- مستخدم إدمن واحد
- قاعدة بيانات فارغة جاهزة للتسجيلات

## 🔄 إعادة تشغيل النظام:

إذا توقف الخادم، ببساطة شغل:
```bash
php -S localhost:8000
```

## 🎉 النظام جاهز للاستخدام!

الآن يمكنك:
- ✅ تسجيل الدخول في الفرونت إند
- ✅ التسجيل في المؤتمر
- ✅ إدارة التسجيلات من لوحة الإدمن
- ✅ مراقبة الإحصائيات
- ✅ استخدام جميع APIs

## 📝 ملاحظات:

- النظام يستخدم SQLite (ملف database/simple.db)
- CORS مفعل للفرونت إند على localhost:3000
- جميع APIs تدعم JSON
- لوحة الإدمن responsive وتعمل على الموبايل

## 🚀 للتطوير المستقبلي:

يمكن بسهولة إضافة:
- المزيد من APIs
- إدارة المتحدثين
- إدارة الأخبار
- نظام الإشعارات
- تصدير البيانات
