import { apiClient, ApiResponse } from '../lib/api';

export interface RegistrationRequest {
  conference_id: number;
  full_name: string;
  email: string;
  phone: string;
  organization: string;
  position: string;
  country: string;
  participant_type: 'local' | 'international';
  registration_type: 'individual' | 'group';
  dietary_requirements?: string;
  special_needs?: string;
  payment_method: 'bank_transfer' | 'cash' | 'online';
  terms_accepted: boolean;
}

export interface Registration {
  id: number;
  confirmation_code: string;
  full_name: string;
  email: string;
  phone: string;
  organization: string;
  position: string;
  country: string;
  participant_type: string;
  registration_type: string;
  registration_fee: number;
  currency: string;
  payment_status: 'pending' | 'paid' | 'failed';
  status: 'pending' | 'confirmed' | 'cancelled';
  is_confirmed: boolean;
  checked_in: boolean;
  conference_title: string;
  created_at: string;
}

export interface RegistrationStatusRequest {
  email: string;
  confirmation_code?: string;
}

export const registrationService = {
  // Submit new registration
  async submitRegistration(data: RegistrationRequest): Promise<ApiResponse<Registration>> {
    return apiClient.post<Registration>('/registrations', data);
  },

  // Get registration by confirmation code
  async getRegistration(confirmationCode: string): Promise<ApiResponse<Registration>> {
    return apiClient.get<Registration>(`/registrations/${confirmationCode}`);
  },

  // Check registration status
  async checkStatus(data: RegistrationStatusRequest): Promise<ApiResponse<Registration>> {
    return apiClient.post<Registration>('/registrations/check-status', data);
  }
};
