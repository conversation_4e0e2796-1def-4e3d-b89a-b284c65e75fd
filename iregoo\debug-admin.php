<?php
/**
 * Debug Admin Login
 */

session_start();

echo "<h2>Admin Login Debug</h2>";

// Database connection
$dbFile = __DIR__ . '/database/simple.db';

echo "<p>Database file: $dbFile</p>";
echo "<p>File exists: " . (file_exists($dbFile) ? 'Yes' : 'No') . "</p>";

if (!file_exists($dbFile)) {
    echo "<p style='color: red;'>Database file does not exist! Creating it...</p>";
    include 'test-db.php';
    exit();
}

$pdo = new PDO('sqlite:' . $dbFile);

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h3>Form Submitted</h3>";
    echo "<p>Email: " . htmlspecialchars($_POST['email'] ?? 'Not provided') . "</p>";
    echo "<p>Password: " . (isset($_POST['password']) ? '[PROVIDED]' : '[NOT PROVIDED]') . "</p>";
    
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    
    // Check user in database
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute([$email]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        echo "<p style='color: green;'>User found in database:</p>";
        echo "<ul>";
        echo "<li>ID: " . $user['id'] . "</li>";
        echo "<li>Name: " . htmlspecialchars($user['name']) . "</li>";
        echo "<li>Email: " . htmlspecialchars($user['email']) . "</li>";
        echo "<li>Role: " . htmlspecialchars($user['role']) . "</li>";
        echo "<li>Active: " . ($user['is_active'] ? 'Yes' : 'No') . "</li>";
        echo "</ul>";
        
        // Check password
        if (password_verify($password, $user['password'])) {
            echo "<p style='color: green;'>Password is correct!</p>";
            
            // Check if admin
            if ($user['role'] === 'admin' && $user['is_active']) {
                echo "<p style='color: green;'>User is admin and active!</p>";
                $_SESSION['admin_user'] = $user;
                echo "<p><a href='/admin'>Go to Admin Dashboard</a></p>";
            } else {
                echo "<p style='color: red;'>User is not admin or not active!</p>";
            }
        } else {
            echo "<p style='color: red;'>Password is incorrect!</p>";
        }
    } else {
        echo "<p style='color: red;'>User not found in database!</p>";
    }
}

// Check session
echo "<h3>Session Info</h3>";
if (isset($_SESSION['admin_user'])) {
    echo "<p style='color: green;'>User is logged in:</p>";
    echo "<pre>" . print_r($_SESSION['admin_user'], true) . "</pre>";
} else {
    echo "<p>No user logged in</p>";
}

// Show all users
$stmt = $pdo->query("SELECT * FROM users");
$users = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<h3>All Users in Database:</h3>";
if (empty($users)) {
    echo "<p style='color: red;'>No users found!</p>";
} else {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Role</th><th>Active</th></tr>";
    foreach ($users as $user) {
        echo "<tr>";
        echo "<td>" . $user['id'] . "</td>";
        echo "<td>" . htmlspecialchars($user['name']) . "</td>";
        echo "<td>" . htmlspecialchars($user['email']) . "</td>";
        echo "<td>" . htmlspecialchars($user['role']) . "</td>";
        echo "<td>" . ($user['is_active'] ? 'Yes' : 'No') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

?>

<h3>Test Login Form</h3>
<form method="POST">
    <p>
        <label>Email:</label><br>
        <input type="email" name="email" value="<EMAIL>" style="width: 300px;">
    </p>
    <p>
        <label>Password:</label><br>
        <input type="password" name="password" value="admin123" style="width: 300px;">
    </p>
    <p>
        <button type="submit">Test Login</button>
    </p>
</form>

<hr>
<p><a href="/admin">Go to Admin Panel</a></p>
<p><a href="/test-db.php">Initialize Database</a></p>
