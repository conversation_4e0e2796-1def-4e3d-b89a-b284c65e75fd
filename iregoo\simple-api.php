<?php
/**
 * Simple API for IREGO Conference
 * This is a standalone PHP API that works without Laravel dependencies
 */

// Enable CORS
header('Access-Control-Allow-Origin: http://localhost:3000');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Set content type
header('Content-Type: application/json');

// Database configuration
$dbFile = __DIR__ . '/database/simple.db';

// Initialize SQLite database
function initDatabase() {
    global $dbFile;
    
    if (!file_exists($dbFile)) {
        $pdo = new PDO('sqlite:' . $dbFile);
        
        // Create tables
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                email TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                role TEXT DEFAULT 'user',
                is_active INTEGER DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ");
        
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS conferences (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                subtitle TEXT,
                description TEXT,
                start_date DATE,
                end_date DATE,
                location TEXT,
                venue TEXT,
                registration_fee_local DECIMAL(10,2),
                registration_fee_international DECIMAL(10,2),
                currency_local TEXT DEFAULT 'LYD',
                currency_international TEXT DEFAULT 'EUR',
                is_active INTEGER DEFAULT 1,
                is_current INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ");
        
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS registrations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                conference_id INTEGER,
                full_name TEXT NOT NULL,
                email TEXT NOT NULL,
                phone TEXT,
                country TEXT,
                organization TEXT,
                position TEXT,
                participant_type TEXT DEFAULT 'local',
                registration_type TEXT DEFAULT 'individual',
                dietary_requirements TEXT,
                special_needs TEXT,
                payment_method TEXT DEFAULT 'cash',
                registration_fee DECIMAL(10,2),
                currency TEXT,
                payment_status TEXT DEFAULT 'pending',
                confirmation_code TEXT UNIQUE,
                is_confirmed INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ");
        
        // Insert sample data
        $pdo->exec("
            INSERT INTO users (name, email, password, role) VALUES 
            ('Super Admin', '<EMAIL>', '" . password_hash('admin123', PASSWORD_DEFAULT) . "', 'admin')
        ");
        
        $pdo->exec("
            INSERT INTO conferences (title, subtitle, description, start_date, end_date, location, venue, registration_fee_local, registration_fee_international, is_active, is_current) VALUES 
            ('International Renewable Energy, Gas & Oil, and Climate Change Conference', 'IREGO Conference 2025', 'A pioneering conference bringing together experts in renewable energy, oil & gas, and climate change solutions.', '2025-11-25', '2025-11-27', 'Tripoli, Libya', 'Conference Center Tripoli', 200.00, 200.00, 1, 1)
        ");
    }
    
    return new PDO('sqlite:' . $dbFile);
}

// Get database connection
function getDB() {
    return initDatabase();
}

// Helper functions
function jsonResponse($data, $status = 200) {
    http_response_code($status);
    echo json_encode($data);
    exit();
}

function getRequestData() {
    return json_decode(file_get_contents('php://input'), true) ?: [];
}

function generateConfirmationCode($email) {
    return 'IREGO-' . strtoupper(substr(md5($email . time()), 0, 8));
}

// Route handling
$requestUri = $_SERVER['REQUEST_URI'];
$requestMethod = $_SERVER['REQUEST_METHOD'];

// Remove query string
$path = parse_url($requestUri, PHP_URL_PATH);

// Remove base path if running in subdirectory
$basePath = '/api/v1';
if (strpos($path, $basePath) === 0) {
    $path = substr($path, strlen($basePath));
}

// Routes
switch ($requestMethod) {
    case 'GET':
        handleGetRequest($path);
        break;
    case 'POST':
        handlePostRequest($path);
        break;
    default:
        jsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
}

function handleGetRequest($path) {
    $db = getDB();
    
    switch ($path) {
        case '/conferences/current':
            $stmt = $db->query("SELECT * FROM conferences WHERE is_current = 1 AND is_active = 1 LIMIT 1");
            $conference = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($conference) {
                jsonResponse(['success' => true, 'data' => $conference]);
            } else {
                jsonResponse(['success' => false, 'message' => 'No current conference found'], 404);
            }
            break;
            
        case '/conferences':
            $stmt = $db->query("SELECT * FROM conferences WHERE is_active = 1 ORDER BY start_date DESC");
            $conferences = $stmt->fetchAll(PDO::FETCH_ASSOC);
            jsonResponse(['success' => true, 'data' => $conferences]);
            break;
            
        case '/speakers':
            jsonResponse(['success' => true, 'data' => []]);
            break;
            
        case '/news':
            jsonResponse(['success' => true, 'data' => []]);
            break;
            
        case '/sponsors':
            jsonResponse(['success' => true, 'data' => []]);
            break;
            
        case '/events':
            jsonResponse(['success' => true, 'data' => []]);
            break;
            
        default:
            jsonResponse(['success' => false, 'message' => 'Endpoint not found'], 404);
    }
}

function handlePostRequest($path) {
    $db = getDB();
    $data = getRequestData();
    
    switch ($path) {
        case '/auth/login':
            if (!isset($data['email']) || !isset($data['password'])) {
                jsonResponse(['success' => false, 'message' => 'Email and password required'], 400);
            }
            
            $stmt = $db->prepare("SELECT * FROM users WHERE email = ? AND is_active = 1");
            $stmt->execute([$data['email']]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user && password_verify($data['password'], $user['password'])) {
                $token = base64_encode(json_encode(['user_id' => $user['id'], 'exp' => time() + 3600]));
                jsonResponse([
                    'success' => true,
                    'message' => 'Login successful',
                    'data' => [
                        'token' => $token,
                        'token_type' => 'bearer',
                        'expires_in' => 3600,
                        'user' => [
                            'id' => $user['id'],
                            'name' => $user['name'],
                            'email' => $user['email'],
                            'role' => $user['role']
                        ]
                    ]
                ]);
            } else {
                jsonResponse(['success' => false, 'message' => 'Invalid credentials'], 401);
            }
            break;
            
        case '/auth/register':
            if (!isset($data['name']) || !isset($data['email']) || !isset($data['password'])) {
                jsonResponse(['success' => false, 'message' => 'Name, email and password required'], 400);
            }
            
            // Check if user exists
            $stmt = $db->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute([$data['email']]);
            if ($stmt->fetch()) {
                jsonResponse(['success' => false, 'message' => 'Email already exists'], 400);
            }
            
            // Create user
            $stmt = $db->prepare("INSERT INTO users (name, email, password) VALUES (?, ?, ?)");
            $stmt->execute([
                $data['name'],
                $data['email'],
                password_hash($data['password'], PASSWORD_DEFAULT)
            ]);
            
            $userId = $db->lastInsertId();
            $token = base64_encode(json_encode(['user_id' => $userId, 'exp' => time() + 3600]));
            
            jsonResponse([
                'success' => true,
                'message' => 'User registered successfully',
                'data' => [
                    'token' => $token,
                    'token_type' => 'bearer',
                    'expires_in' => 3600,
                    'user' => [
                        'id' => $userId,
                        'name' => $data['name'],
                        'email' => $data['email'],
                        'role' => 'user'
                    ]
                ]
            ], 201);
            break;
            
        case '/registrations':
            // Validate required fields
            $required = ['conference_id', 'full_name', 'email', 'country', 'participant_type', 'registration_type', 'payment_method'];
            foreach ($required as $field) {
                if (!isset($data[$field]) || empty($data[$field])) {
                    jsonResponse(['success' => false, 'message' => "Field $field is required"], 400);
                }
            }
            
            // Get conference
            $stmt = $db->prepare("SELECT * FROM conferences WHERE id = ? AND is_active = 1");
            $stmt->execute([$data['conference_id']]);
            $conference = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$conference) {
                jsonResponse(['success' => false, 'message' => 'Conference not found'], 404);
            }
            
            // Calculate fee
            $fee = $data['participant_type'] === 'local' ? $conference['registration_fee_local'] : $conference['registration_fee_international'];
            $currency = $data['participant_type'] === 'local' ? $conference['currency_local'] : $conference['currency_international'];
            
            // Generate confirmation code
            $confirmationCode = generateConfirmationCode($data['email']);
            
            // Insert registration
            $stmt = $db->prepare("
                INSERT INTO registrations (
                    conference_id, full_name, email, phone, country, organization, position,
                    participant_type, registration_type, dietary_requirements, special_needs,
                    payment_method, registration_fee, currency, confirmation_code
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $data['conference_id'],
                $data['full_name'],
                $data['email'],
                $data['phone'] ?? null,
                $data['country'],
                $data['organization'] ?? null,
                $data['position'] ?? null,
                $data['participant_type'],
                $data['registration_type'],
                $data['dietary_requirements'] ?? null,
                $data['special_needs'] ?? null,
                $data['payment_method'],
                $fee,
                $currency,
                $confirmationCode
            ]);
            
            jsonResponse([
                'success' => true,
                'message' => 'Registration submitted successfully',
                'data' => [
                    'id' => $db->lastInsertId(),
                    'confirmation_code' => $confirmationCode,
                    'full_name' => $data['full_name'],
                    'email' => $data['email'],
                    'participant_type' => $data['participant_type'],
                    'registration_type' => $data['registration_type'],
                    'registration_fee' => $fee,
                    'currency' => $currency,
                    'payment_status' => 'pending'
                ]
            ], 201);
            break;
            
        default:
            jsonResponse(['success' => false, 'message' => 'Endpoint not found'], 404);
    }
}
?>
