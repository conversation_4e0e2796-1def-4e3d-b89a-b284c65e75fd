@echo off
echo 🚀 Setting up IREGO Conference Backend...

REM Install dependencies
echo 📦 Installing Composer dependencies...
composer install

REM Generate application key
echo 🔑 Generating application key...
php artisan key:generate

REM Generate JWT secret
echo 🔐 Generating JWT secret...
php artisan jwt:secret

REM Create database file if using SQLite
echo 🗄️ Setting up database...
type nul > database\database.sqlite

REM Run migrations
echo 📊 Running database migrations...
php artisan migrate:fresh

REM Seed database with sample data
echo 🌱 Seeding database with sample data...
php artisan db:seed

REM Create storage link
echo 🔗 Creating storage link...
php artisan storage:link

REM Clear caches
echo 🧹 Clearing caches...
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear

echo ✅ Backend setup complete!
echo.
echo 🎯 Quick Start:
echo    Admin Panel: http://localhost:8000/admin
echo    API Base URL: http://localhost:8000/api/v1
echo    Default Admin: <EMAIL> / admin123
echo.
echo 🚀 To start the server:
echo    php artisan serve

pause
