'use client';

import React, { useState, useEffect } from 'react';
import { conferenceService } from '../services/conferences';
import { handleApiError } from '../lib/api';

const ApiStatus: React.FC = () => {
  const [status, setStatus] = useState<'checking' | 'connected' | 'error'>('checking');
  const [message, setMessage] = useState('Checking API connection...');
  const [conferenceData, setConferenceData] = useState<any>(null);

  useEffect(() => {
    const checkApiConnection = async () => {
      try {
        setStatus('checking');
        setMessage('Connecting to API...');

        // Test API connection by fetching current conference
        const response = await conferenceService.getCurrentConference();
        
        if (response.success && response.data) {
          setStatus('connected');
          setMessage('API connected successfully!');
          setConferenceData(response.data);
        } else {
          setStatus('error');
          setMessage(response.message || 'No current conference found');
        }
      } catch (error: any) {
        setStatus('error');
        setMessage(handleApiError(error));
      }
    };

    checkApiConnection();
  }, []);

  const getStatusColor = () => {
    switch (status) {
      case 'checking':
        return 'bg-yellow-100 border-yellow-400 text-yellow-800';
      case 'connected':
        return 'bg-green-100 border-green-400 text-green-800';
      case 'error':
        return 'bg-red-100 border-red-400 text-red-800';
      default:
        return 'bg-gray-100 border-gray-400 text-gray-800';
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'checking':
        return '⏳';
      case 'connected':
        return '✅';
      case 'error':
        return '❌';
      default:
        return '❓';
    }
  };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className={`p-3 rounded-lg border-2 shadow-lg max-w-sm ${getStatusColor()}`}>
        <div className="flex items-center gap-2 mb-2">
          <span className="text-lg">{getStatusIcon()}</span>
          <span className="font-semibold">API Status</span>
        </div>
        <p className="text-sm">{message}</p>
        
        {conferenceData && (
          <div className="mt-2 text-xs">
            <p><strong>Conference:</strong> {conferenceData.title}</p>
            <p><strong>Location:</strong> {conferenceData.location}</p>
            <p><strong>Date:</strong> {new Date(conferenceData.start_date).toLocaleDateString()}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ApiStatus;
