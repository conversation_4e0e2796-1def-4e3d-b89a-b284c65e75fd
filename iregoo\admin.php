<?php
/**
 * Simple Admin Panel for IREGO Conference
 */

session_start();

// Database connection
$dbFile = __DIR__ . '/database/simple.db';

function getAdminDB() {
    global $dbFile;
    if (!file_exists($dbFile)) {
        // Initialize database if it doesn't exist
        initAdminDatabase();
    }
    return new PDO('sqlite:' . $dbFile);
}

function initAdminDatabase() {
    global $dbFile;

    $pdo = new PDO('sqlite:' . $dbFile);

    // Create tables
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            email TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            role TEXT DEFAULT 'user',
            is_active INTEGER DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ");

    $pdo->exec("
        CREATE TABLE IF NOT EXISTS conferences (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            subtitle TEXT,
            description TEXT,
            start_date DATE,
            end_date DATE,
            location TEXT,
            venue TEXT,
            registration_fee_local DECIMAL(10,2),
            registration_fee_international DECIMAL(10,2),
            currency_local TEXT DEFAULT 'LYD',
            currency_international TEXT DEFAULT 'EUR',
            is_active INTEGER DEFAULT 1,
            is_current INTEGER DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ");

    $pdo->exec("
        CREATE TABLE IF NOT EXISTS registrations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            conference_id INTEGER,
            full_name TEXT NOT NULL,
            email TEXT NOT NULL,
            phone TEXT,
            country TEXT,
            organization TEXT,
            position TEXT,
            participant_type TEXT DEFAULT 'local',
            registration_type TEXT DEFAULT 'individual',
            dietary_requirements TEXT,
            special_needs TEXT,
            payment_method TEXT DEFAULT 'cash',
            registration_fee DECIMAL(10,2),
            currency TEXT,
            payment_status TEXT DEFAULT 'pending',
            confirmation_code TEXT UNIQUE,
            is_confirmed INTEGER DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ");

    // Insert sample data
    $pdo->exec("
        INSERT OR IGNORE INTO users (name, email, password, role) VALUES
        ('Super Admin', '<EMAIL>', '" . password_hash('admin123', PASSWORD_DEFAULT) . "', 'admin')
    ");

    $pdo->exec("
        INSERT OR IGNORE INTO conferences (title, subtitle, description, start_date, end_date, location, venue, registration_fee_local, registration_fee_international, is_active, is_current) VALUES
        ('International Renewable Energy, Gas & Oil, and Climate Change Conference', 'IREGO Conference 2025', 'A pioneering conference bringing together experts in renewable energy, oil & gas, and climate change solutions.', '2025-11-25', '2025-11-27', 'Tripoli, Libya', 'Conference Center Tripoli', 200.00, 200.00, 1, 1)
    ");

    return $pdo;
}

// Check if user is logged in
function isLoggedIn() {
    return isset($_SESSION['admin_user']);
}

// Handle login
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['login'])) {
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    
    $db = getAdminDB();
    $stmt = $db->prepare("SELECT * FROM users WHERE email = ? AND role = 'admin' AND is_active = 1");
    $stmt->execute([$email]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user && password_verify($password, $user['password'])) {
        $_SESSION['admin_user'] = $user;
        header('Location: admin.php');
        exit();
    } else {
        $error = 'Invalid credentials';
    }
}

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: admin.php');
    exit();
}

// If not logged in, show login form
if (!isLoggedIn()) {
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Admin Login - IREGO Conference</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
            body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; display: flex; align-items: center; }
            .login-card { background: white; border-radius: 15px; box-shadow: 0 15px 35px rgba(0,0,0,0.1); }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-4">
                    <div class="login-card p-4">
                        <h3 class="text-center mb-4">Admin Login</h3>
                        <?php if (isset($error)): ?>
                            <div class="alert alert-danger"><?= htmlspecialchars($error) ?></div>
                        <?php endif; ?>
                        <form method="POST">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                            <button type="submit" name="login" class="btn btn-primary w-100">Login</button>
                        </form>
                        <div class="text-center mt-3">
                            <small class="text-muted">Default: <EMAIL> / admin123</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit();
}

// Admin dashboard
$db = getAdminDB();

// Get statistics
$stats = [];
$stats['conferences'] = $db->query("SELECT COUNT(*) FROM conferences")->fetchColumn();
$stats['registrations'] = $db->query("SELECT COUNT(*) FROM registrations")->fetchColumn();
$stats['confirmed'] = $db->query("SELECT COUNT(*) FROM registrations WHERE is_confirmed = 1")->fetchColumn();
$stats['pending'] = $db->query("SELECT COUNT(*) FROM registrations WHERE is_confirmed = 0")->fetchColumn();

// Get recent registrations
$recentRegistrations = $db->query("
    SELECT r.*, c.title as conference_title 
    FROM registrations r 
    LEFT JOIN conferences c ON r.conference_id = c.id 
    ORDER BY r.created_at DESC 
    LIMIT 10
")->fetchAll(PDO::FETCH_ASSOC);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - IREGO Conference</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">IREGO Admin</a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">Welcome, <?= htmlspecialchars($_SESSION['admin_user']['name']) ?></span>
                <a class="nav-link" href="?logout=1">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <h1>Dashboard</h1>
                
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-white bg-primary">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?= $stats['conferences'] ?></h4>
                                        <p>Conferences</p>
                                    </div>
                                    <div><i class="fas fa-calendar-alt fa-2x"></i></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-white bg-success">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?= $stats['registrations'] ?></h4>
                                        <p>Total Registrations</p>
                                    </div>
                                    <div><i class="fas fa-users fa-2x"></i></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-white bg-info">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?= $stats['confirmed'] ?></h4>
                                        <p>Confirmed</p>
                                    </div>
                                    <div><i class="fas fa-check-circle fa-2x"></i></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-white bg-warning">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?= $stats['pending'] ?></h4>
                                        <p>Pending</p>
                                    </div>
                                    <div><i class="fas fa-clock fa-2x"></i></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Registrations -->
                <div class="card">
                    <div class="card-header">
                        <h5>Recent Registrations</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recentRegistrations)): ?>
                            <p class="text-muted">No registrations yet.</p>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Email</th>
                                            <th>Country</th>
                                            <th>Type</th>
                                            <th>Status</th>
                                            <th>Date</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recentRegistrations as $reg): ?>
                                        <tr>
                                            <td><?= htmlspecialchars($reg['full_name']) ?></td>
                                            <td><?= htmlspecialchars($reg['email']) ?></td>
                                            <td><?= htmlspecialchars($reg['country']) ?></td>
                                            <td>
                                                <span class="badge bg-<?= $reg['participant_type'] === 'local' ? 'info' : 'warning' ?>">
                                                    <?= ucfirst($reg['participant_type']) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?= $reg['is_confirmed'] ? 'success' : 'secondary' ?>">
                                                    <?= $reg['is_confirmed'] ? 'Confirmed' : 'Pending' ?>
                                                </span>
                                            </td>
                                            <td><?= date('M d, Y', strtotime($reg['created_at'])) ?></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- API Information -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5>API Information</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>API Base URL:</strong> <code><?= (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] ?>/api/v1</code></p>
                        <p><strong>Frontend URL:</strong> <code>http://localhost:3000</code></p>
                        <p><strong>Status:</strong> <span class="badge bg-success">Active</span></p>
                        
                        <h6>Available Endpoints:</h6>
                        <ul>
                            <li><code>GET /api/v1/conferences/current</code> - Current conference</li>
                            <li><code>GET /api/v1/conferences</code> - All conferences</li>
                            <li><code>POST /api/v1/auth/login</code> - User login</li>
                            <li><code>POST /api/v1/auth/register</code> - User registration</li>
                            <li><code>POST /api/v1/registrations</code> - Conference registration</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
