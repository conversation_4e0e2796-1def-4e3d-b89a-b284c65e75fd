{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya/lipya1/src/app/expo/page.tsx"], "sourcesContent": ["import React from 'react';\r\n\r\nexport default function About() {\r\n  return (\r\n    <div>\r\n      {/* صورة الهيرو */}\r\n      <div className=\"relative h-[300px] md:h-[400px] w-full\">\r\n        <img\r\n          src=\"/photo1.png\"\r\n          alt=\"About IREGO\"\r\n          className=\"object-cover w-full h-full\"\r\n        />\r\n        <div className=\"absolute inset-0 bg-black/50 flex items-center justify-center\">\r\n          <h1 className=\"text-3xl md:text-5xl font-bold text-white\">\r\n            IREGO <span className=\"text-orange-500\">EXPO</span>\r\n          </h1>\r\n        </div>\r\n      </div>\r\n\r\n      {/* مقدمة */}\r\n      <div className=\"mt-10 text-center px-4 max-w-3xl mx-auto\">\r\n        <h1 className=\"text-lg md:text-3xl font-semibold\">\r\n          IREGO <span className=\"text-orange-500 uppercase font-bold\">EXPO</span>\r\n        </h1>\r\n        <p className=\"text-gray-700 text-sm md:text-base mb-8\">\r\n          Exhibition on Renewable Energy, Oil, Gas, and Climate Change\r\n          <br />\r\n          <span>\r\n            A unique platform connecting companies and institutions in the field of sustainable energy and climate solutions. Join us in shaping the future of energy and environmental sustainability.\r\n          </span>\r\n        </p>\r\n      </div>\r\n\r\n      {/* صورة + شرح المعرض */}\r\n      <div className=\"max-w-7xl mx-auto px-4 py-12 flex flex-col md:flex-row items-start gap-8\">\r\n        <div className=\"w-full md:w-1/2\">\r\n          <img\r\n            src=\"/photoo9.png\"\r\n            alt=\"Conference Visual\"\r\n            className=\"w-full h-auto rounded-lg\"\r\n          />\r\n        </div>\r\n        <div className=\"w-full md:w-1/2 space-y-6\">\r\n          <h2 className=\"text-lg md:text-3xl font-semibold\">\r\n            ABOUT THE <span className=\"text-orange-500 uppercase font-bold\">EXPO</span>\r\n          </h2>\r\n          <p className=\"mt-2 text-gray-700 leading-relaxed\">\r\n            The IREGO EXPO provides an opportunity to showcase the latest innovations, technologies, and solutions in renewable energy, oil, gas, and climate-related sectors. It encourages interaction and collaboration between industry leaders, startups, and research institutions.\r\n          </p>\r\n          <p className=\"mt-2 text-gray-700 leading-relaxed\">\r\n            Our platform serves as a catalyst for meaningful discussions, partnerships, and breakthrough innovations in sustainable energy solutions. Join us in creating a more sustainable future for generations to come.\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      {/* أهداف المعرض */}\r\n      <div className=\"mt-10 text-center px-4 max-w-3xl mx-auto\">\r\n        <h1 className=\"text-lg md:text-3xl font-semibold\">\r\n          Objectives of <span className=\"text-orange-500 uppercase font-bold\">the Expo</span>\r\n        </h1>\r\n      </div>\r\n\r\n      {/* قائمة الأهداف */}\r\n      <div className=\"transition duration-700 ease-in-out max-w-5xl mx-auto mt-10 px-4 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6\">\r\n        {[\r\n          {\r\n            name: 'Innovation Showcase',\r\n            img: '/a1.png',\r\n            role: 'Present the latest innovations in energy and climate solutions',\r\n          },\r\n          {\r\n            name: 'Industry Collaboration',\r\n            img: '/a2.png',\r\n            role: 'Foster partnerships between companies and institutions',\r\n          },\r\n          {\r\n            name: 'Knowledge Sharing',\r\n            img: '/a3.png',\r\n            role: 'Raise awareness and promote dialogue on sustainability',\r\n          },\r\n          {\r\n            name: 'Environmental Impact',\r\n            img: '/a4.png',\r\n            role: 'Support initiatives for clean and sustainable energy',\r\n          },\r\n          {\r\n            name: 'Economic Growth',\r\n            img: '/a5.png',\r\n            role: 'Stimulate green economic growth and innovation',\r\n          },\r\n        ].map((item, index) => (\r\n          <div key={index} className=\"bg-white shadow-md rounded-lg p-6\">\r\n            <div className=\"flex items-center gap-3 mb-2\">\r\n              <img\r\n                src={item.img}\r\n                alt={item.name}\r\n                className=\"w-14 h-14 object-cover rounded-full\"\r\n              />\r\n              <div>\r\n                <h3 className=\"text-lg font-semibold\">{item.name}</h3>\r\n                <p className=\"text-sm text-gray-600\">{item.role}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n      {/* Key Themes & Focus Areas */}\r\n      <div className=\"mt-10 text-center px-4 max-w-3xl mx-auto\">\r\n        <h1 className=\"text-lg md:text-3xl font-semibold\">\r\n          Key Themes & <span className=\"text-orange-500 uppercase font-bold\">Focus Areas</span>\r\n        </h1>\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 p-4 mt-6\">\r\n        {[\r\n          {\r\n            img: '/m1.png',\r\n            title: 'Renewable Energy',\r\n            points: ['Solar, wind, geothermal, and biomass solutions'],\r\n          },\r\n          {\r\n            img: '/m2.png',\r\n            title: 'Oil & Gas',\r\n            points: ['Sustainability strategies and future technologies'],\r\n          },\r\n          {\r\n            img: '/m3.png',\r\n            title: 'Technological Innovation',\r\n            points: ['AI, robotics, and digital monitoring solutions'],\r\n          },\r\n          {\r\n            img: '/m4.png',\r\n            title: 'Climate Change',\r\n            points: ['Mitigation strategies and environmental impact'],\r\n          },\r\n          {\r\n            img: '/m1.png',\r\n            title: 'Environmental Startups',\r\n            points: ['Project ideas for environmental protection'],\r\n          },\r\n        ].map((section, idx) => (\r\n          <div key={idx} className=\"bg-white rounded-2xl shadow-md p-4 text-center\">\r\n            <img src={section.img} alt={section.title} className=\"w-66 h-46 mx-auto mb-2\" />\r\n            <h2 className=\"text-orange-500 text-lg font-bold mb-3\">{section.title}</h2>\r\n            <ul className=\"text-gray-700 space-y-1 text-sm\">\r\n              {section.points.map((point, i) => (\r\n                <li key={i}>{point}</li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n\r\n\r\n<div className=\"bg-orange-400  py-16 px-6 text-center\">\r\n  <h2 className=\"text-3xl md:text-5xl font-bold mb-4 text-white\">\r\n    Join the Future of Energy\r\n  </h2>\r\n  <p className=\"max-w-3xl  mx-auto text-sm md:text-lg mb-8 text-gray-700\">\r\n    Be part of the transformation towards a sustainable energy future. Connect with industry leaders, discover innovative solutions, and shape the future of energy.\r\n  </p>\r\n\r\n  <div className=\"flex flex-col sm:flex-row justify-center gap-4 mt-6\">\r\n    <button className=\"bg-white text-orange-500 font-semibold py-3 px-6 rounded-full hover:bg-gray-100 transition\">\r\n      Register Now\r\n    </button>\r\n    <button className=\"bg-orange-500 text-gray-700 font-semibold py-3 px-6 rounded-full hover:bg-orange-700 transition\">\r\n      Become an Exhibitor\r\n    </button>\r\n  </div>\r\n</div>\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;;0BAEC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,KAAI;wBACJ,KAAI;wBACJ,WAAU;;;;;;kCAEZ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;;gCAA4C;8CAClD,8OAAC;oCAAK,WAAU;8CAAkB;;;;;;;;;;;;;;;;;;;;;;;0BAM9C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;4BAAoC;0CAC1C,8OAAC;gCAAK,WAAU;0CAAsC;;;;;;;;;;;;kCAE9D,8OAAC;wBAAE,WAAU;;4BAA0C;0CAErD,8OAAC;;;;;0CACD,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;0BAOV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,KAAI;4BACJ,KAAI;4BACJ,WAAU;;;;;;;;;;;kCAGd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAoC;kDACtC,8OAAC;wCAAK,WAAU;kDAAsC;;;;;;;;;;;;0CAElE,8OAAC;gCAAE,WAAU;0CAAqC;;;;;;0CAGlD,8OAAC;gCAAE,WAAU;0CAAqC;;;;;;;;;;;;;;;;;;0BAOtD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;;wBAAoC;sCAClC,8OAAC;4BAAK,WAAU;sCAAsC;;;;;;;;;;;;;;;;;0BAKxE,8OAAC;gBAAI,WAAU;0BACZ;oBACC;wBACE,MAAM;wBACN,KAAK;wBACL,MAAM;oBACR;oBACA;wBACE,MAAM;wBACN,KAAK;wBACL,MAAM;oBACR;oBACA;wBACE,MAAM;wBACN,KAAK;wBACL,MAAM;oBACR;oBACA;wBACE,MAAM;wBACN,KAAK;wBACL,MAAM;oBACR;oBACA;wBACE,MAAM;wBACN,KAAK;wBACL,MAAM;oBACR;iBACD,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC;wBAAgB,WAAU;kCACzB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,KAAK,KAAK,GAAG;oCACb,KAAK,KAAK,IAAI;oCACd,WAAU;;;;;;8CAEZ,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAyB,KAAK,IAAI;;;;;;sDAChD,8OAAC;4CAAE,WAAU;sDAAyB,KAAK,IAAI;;;;;;;;;;;;;;;;;;uBAT3C;;;;;;;;;;0BAiBd,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;;wBAAoC;sCACnC,8OAAC;4BAAK,WAAU;sCAAsC;;;;;;;;;;;;;;;;;0BAIvE,8OAAC;gBAAI,WAAU;0BACZ;oBACC;wBACE,KAAK;wBACL,OAAO;wBACP,QAAQ;4BAAC;yBAAiD;oBAC5D;oBACA;wBACE,KAAK;wBACL,OAAO;wBACP,QAAQ;4BAAC;yBAAoD;oBAC/D;oBACA;wBACE,KAAK;wBACL,OAAO;wBACP,QAAQ;4BAAC;yBAAiD;oBAC5D;oBACA;wBACE,KAAK;wBACL,OAAO;wBACP,QAAQ;4BAAC;yBAAiD;oBAC5D;oBACA;wBACE,KAAK;wBACL,OAAO;wBACP,QAAQ;4BAAC;yBAA6C;oBACxD;iBACD,CAAC,GAAG,CAAC,CAAC,SAAS,oBACd,8OAAC;wBAAc,WAAU;;0CACvB,8OAAC;gCAAI,KAAK,QAAQ,GAAG;gCAAE,KAAK,QAAQ,KAAK;gCAAE,WAAU;;;;;;0CACrD,8OAAC;gCAAG,WAAU;0CAA0C,QAAQ,KAAK;;;;;;0CACrE,8OAAC;gCAAG,WAAU;0CACX,QAAQ,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,kBAC1B,8OAAC;kDAAY;uCAAJ;;;;;;;;;;;uBALL;;;;;;;;;;0BAcpB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAiD;;;;;;kCAG/D,8OAAC;wBAAE,WAAU;kCAA2D;;;;;;kCAIxE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAO,WAAU;0CAA6F;;;;;;0CAG/G,8OAAC;gCAAO,WAAU;0CAAkG;;;;;;;;;;;;;;;;;;;;;;;;AAcxH", "debugId": null}}, {"offset": {"line": 487, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya/lipya1/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 525, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya/lipya1/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;AAAqC,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;AAEgG,EAAC;IA2BrHiB,UAAU;;;;;;;;;AAlBZ,OAAO,MAAMd,eAAe,6CAAA;IAC1BC,MAAAA,GAASC;IACTC,EAAAA,OAAAA;IAAAA,CAAWC;IAAAA;QACb,EAAC,UAAA;YAAA;YAAA;gBAED,YAAA;oBAAA,CAAc;oBAAA,+BAA0C;qBAAE,wBAAwB;wBAAuB,UAAA,CAAA;wBAAA,QAAA;4BAAA,IAAA;4BAAA;yBAAA;oBAEzG;iBAAA,0DAA4D;YAC5D;YAAA,IAAO,MAAMC,cAAc,IAAIX,mBAAmB;kBAChDY,QAAAA,CAAAA,CAAY;YAAA;SAAA;;SACVC,MAAMZ,UAAUa,QAAQ;cACxBC,IAAAA;YAAAA,CAAM,GAAA;YAAA;SAAA;cACNC,OAAAA;YAAAA,EAAU,EAAA;YAAA;SAAA;cACV,OAAA;YAAA,IAAA,6BAA2C;YAAA;SAAA;cAC3CC,UAAAA;YAAAA,CAAY,GAAA;YAAA;SAAA;;OACZC,UAAU;QACVC,MAAAA;IAAAA,GAAU,EAAE;CAAA;;;AAKhB,GAAE,GAAA,uBAAA,sBAAA,CAAA", "ignoreList": [0], "debugId": null}}]}