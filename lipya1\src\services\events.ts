import { apiClient, ApiResponse } from '../lib/api';

export interface Event {
  id: number;
  title: string;
  description: string;
  start_time: string;
  end_time: string;
  location: string;
  event_type: 'session' | 'break' | 'lunch' | 'keynote' | 'panel' | 'workshop';
  is_featured: boolean;
  conference_id: number;
  speakers?: {
    id: number;
    name: string;
    title: string;
    image_url: string;
  }[];
}

export interface EventFilters {
  conference_id?: number;
  event_type?: string;
  date?: string;
  is_featured?: boolean;
}

export const eventService = {
  // Get all events
  async getEvents(filters?: EventFilters): Promise<ApiResponse<Event[]>> {
    return apiClient.get<Event[]>('/events', filters);
  },

  // Get specific event by ID
  async getEvent(id: number): Promise<ApiResponse<Event>> {
    return apiClient.get<Event>(`/events/${id}`);
  },

  // Get conference schedule
  async getSchedule(): Promise<ApiResponse<Event[]>> {
    return apiClient.get<Event[]>('/events/schedule/all');
  }
};
