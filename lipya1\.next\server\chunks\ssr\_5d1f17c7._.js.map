{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya/lipya1/src/app/the-best/page.tsx"], "sourcesContent": ["function page() {\r\n  return (\r\n    <>\r\n      {/* Hero Section */}\r\n      <div className=\"relative h-[300px] md:h-[400px] w-full\">\r\n        <img\r\n          src=\"/photo1.png\"\r\n          alt=\"About IREGO\"\r\n          className=\"object-cover w-full h-full\"\r\n        />\r\n        <div className=\"absolute inset-0 bg-black/50 flex items-center justify-center\">\r\n          <h1 className=\"text-3xl md:text-5xl font-bold text-white\">\r\n            IREGO <span className=\"text-orange-500\">THE BEST</span>\r\n          </h1>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Award Categories */}\r\n      <section className=\"py-16 px-4 sm:px-6 lg:px-12 text-center\">\r\n        <h1 className=\"text-4xl font-bold\">\r\n          IREGO <span className=\"text-orange-500\">THE BEST</span>\r\n        </h1>\r\n        <p className=\"mt-4 text-lg font-medium\">\r\n          Celebrating Innovation & Excellence in Energy and Climate Fields\r\n        </p>\r\n        <p className=\"mt-2 text-gray-600 max-w-3xl mx-auto text-sm md:text-base\">\r\n          Join us in recognizing outstanding contributions to renewable energy, oil and gas innovation, and climate change solutions. The IREGO conference honors excellence across multiple disciplines.\r\n        </p>\r\n\r\n        <div className=\"mt-12 text-left\">\r\n          <h2 className=\"text-2xl font-semibold mb-8\">\r\n            Award <span className=\"text-orange-500\">Categories</span>\r\n          </h2>\r\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n            {[\r\n              { title: \"Best Researcher in Renewable Energy\", desc: \"Recognizing groundbreaking research in renewable energy technologies\", img: \"a1\" },\r\n              { title: \"Best Media Contributor\", desc: \"Honoring exceptional contributions to environmental journalism\", img: \"a2\" },\r\n              { title: \"Best Technical Innovation\", desc: \"Celebrating innovative technical solutions in energy sector\", img: \"a3\" },\r\n              { title: \"Best Young Innovator\", desc: \"Supporting the next generation of energy sector leaders\", img: \"a4\" },\r\n              { title: \"Best Business Leader\", desc: \"Acknowledging sustainable business practices and leadership\", img: \"a5\" },\r\n              { title: \"Climate Change Solutions\", desc: \"Recognizing innovative approaches to climate challenges\", img: \"a6\" },\r\n            ].map((item, index) => (\r\n              <div key={index} className=\"bg-gray-50 border border-gray-200 rounded-xl p-6 shadow-sm hover:shadow-md transition-all\">\r\n                <div className=\"w-10 h-10 mb-4 bg-orange-100 rounded-full flex items-center justify-center\">\r\n                  <img src={`/${item.img}.png`} alt=\"\" className=\"w-6 h-6\" />\r\n                </div>\r\n                <h3 className=\"text-base font-bold text-gray-800 mb-2\">{item.title}</h3>\r\n                <p className=\"text-sm text-gray-600\">{item.desc}</p>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Required Documents */}\r\n      <section className=\"py-16 px-4 sm:px-6 lg:px-12 text-left\">\r\n        <h2 className=\"text-2xl font-semibold mb-8\">\r\n          Required <span className=\"text-orange-500\">Documents</span>\r\n        </h2>\r\n\r\n        {/* إعادة نفس الجدول 6 مرات - ممكن تستبدله بمكون معاد استخدامه */}\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n          {[...Array(6)].map((_, i) => (\r\n            <div key={i} className=\"bg-gray-50 border border-gray-200 rounded-xl p-6 shadow-sm hover:shadow-md transition-all\">\r\n              <div className=\"w-10 h-10 mb-4 bg-orange-100 rounded-full flex items-center justify-center\">\r\n                <img src={`/a${i + 1}.png`} alt=\"\" className=\"w-6 h-6\" />\r\n              </div>\r\n              <h3 className=\"text-base font-bold text-gray-800 mb-2\">Document Title {i + 1}</h3>\r\n              <p className=\"text-sm text-gray-600\">Description for document {i + 1}</p>\r\n            </div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Evaluation Criteria */}\r\n        <h2 className=\"text-2xl font-semibold mt-16 mb-8\">\r\n          Evaluation <span className=\"text-orange-500\">Criteria</span>\r\n        </h2>\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\">\r\n          {[\r\n            \"Creativity and originality in the work\",\r\n            \"Scientific and practical importance\",\r\n            \"Quality and clarity of the written work\",\r\n            \"Alignment with development goals\",\r\n          ].map((text, index) => (\r\n            <div key={index} className=\"bg-white border border-gray-200 rounded-xl p-6 text-center shadow-sm hover:shadow-md transition-all\">\r\n              <div className=\"text-orange-500 text-2xl mb-3\">\r\n                <img src=\"/Vector (3).png\" alt=\"\" />\r\n              </div>\r\n              <p className=\"text-sm font-medium text-gray-700\">{text}</p>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </section>\r\n    </>\r\n  );\r\n}\r\nexport default page;\r\n"], "names": [], "mappings": ";;;;;AAAA,SAAS;IACP,qBACE;;0BAEE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,KAAI;wBACJ,KAAI;wBACJ,WAAU;;;;;;kCAEZ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;;gCAA4C;8CAClD,8OAAC;oCAAK,WAAU;8CAAkB;;;;;;;;;;;;;;;;;;;;;;;0BAM9C,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAG,WAAU;;4BAAqB;0CAC3B,8OAAC;gCAAK,WAAU;0CAAkB;;;;;;;;;;;;kCAE1C,8OAAC;wBAAE,WAAU;kCAA2B;;;;;;kCAGxC,8OAAC;wBAAE,WAAU;kCAA4D;;;;;;kCAIzE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAA8B;kDACpC,8OAAC;wCAAK,WAAU;kDAAkB;;;;;;;;;;;;0CAE1C,8OAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,OAAO;wCAAuC,MAAM;wCAAwE,KAAK;oCAAK;oCACxI;wCAAE,OAAO;wCAA0B,MAAM;wCAAkE,KAAK;oCAAK;oCACrH;wCAAE,OAAO;wCAA6B,MAAM;wCAA+D,KAAK;oCAAK;oCACrH;wCAAE,OAAO;wCAAwB,MAAM;wCAA2D,KAAK;oCAAK;oCAC5G;wCAAE,OAAO;wCAAwB,MAAM;wCAA+D,KAAK;oCAAK;oCAChH;wCAAE,OAAO;wCAA4B,MAAM;wCAA2D,KAAK;oCAAK;iCACjH,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,KAAK,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,IAAI,CAAC;oDAAE,KAAI;oDAAG,WAAU;;;;;;;;;;;0DAEjD,8OAAC;gDAAG,WAAU;0DAA0C,KAAK,KAAK;;;;;;0DAClE,8OAAC;gDAAE,WAAU;0DAAyB,KAAK,IAAI;;;;;;;uCALvC;;;;;;;;;;;;;;;;;;;;;;0BAalB,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAG,WAAU;;4BAA8B;0CACjC,8OAAC;gCAAK,WAAU;0CAAkB;;;;;;;;;;;;kCAI7C,8OAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;gCAAY,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,KAAK,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC;4CAAE,KAAI;4CAAG,WAAU;;;;;;;;;;;kDAE/C,8OAAC;wCAAG,WAAU;;4CAAyC;4CAAgB,IAAI;;;;;;;kDAC3E,8OAAC;wCAAE,WAAU;;4CAAwB;4CAA0B,IAAI;;;;;;;;+BAL3D;;;;;;;;;;kCAWd,8OAAC;wBAAG,WAAU;;4BAAoC;0CACrC,8OAAC;gCAAK,WAAU;0CAAkB;;;;;;;;;;;;kCAE/C,8OAAC;wBAAI,WAAU;kCACZ;4BACC;4BACA;4BACA;4BACA;yBACD,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC;gCAAgB,WAAU;;kDACzB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,KAAI;4CAAkB,KAAI;;;;;;;;;;;kDAEjC,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;;+BAJ1C;;;;;;;;;;;;;;;;;;AAWtB;uCACe", "debugId": null}}, {"offset": {"line": 396, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya/lipya1/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 434, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya/lipya1/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;AAAqC,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;AAEgG,EAAC;IA2BrHiB,UAAU;;;;;;;;;AAlBZ,OAAO,MAAMd,eAAe,6CAAA;IAC1BC,MAAAA,GAASC;IACTC,EAAAA,OAAAA;IAAAA,CAAWC;IAAAA;QACb,EAAC,UAAA;YAAA;YAAA;gBAED,YAAA;oBAAA,CAAc;oBAAA,+BAA0C;qBAAE,wBAAwB;wBAAuB,UAAA,CAAA;wBAAA,QAAA;4BAAA,IAAA;4BAAA;yBAAA;oBAEzG;iBAAA,0DAA4D;YAC5D;YAAA,IAAO,MAAMC,cAAc,IAAIX,mBAAmB;kBAChDY,QAAAA,CAAAA,CAAY;YAAA;SAAA;;SACVC,MAAMZ,UAAUa,QAAQ;cACxBC,IAAAA;YAAAA,CAAM,GAAA;YAAA;SAAA;cACNC,OAAAA;YAAAA,EAAU,EAAA;YAAA;SAAA;cACV,OAAA;YAAA,IAAA,6BAA2C;YAAA;SAAA;cAC3CC,UAAAA;YAAAA,CAAY,GAAA;YAAA;SAAA;;OACZC,UAAU;QACVC,MAAAA;IAAAA,GAAU,EAAE;CAAA;;;AAKhB,GAAE,GAAA,uBAAA,sBAAA,CAAA", "ignoreList": [0], "debugId": null}}]}