'use client';

import React, { useState, useEffect } from 'react';
import { registrationService, RegistrationRequest } from '../services/registration';
import { conferenceService } from '../services/conferences';
import { handleApiError } from '../lib/api';

interface RegistrationFormProps {
  onSuccess?: (confirmationCode: string) => void;
}

const RegistrationForm: React.FC<RegistrationFormProps> = ({ onSuccess }) => {
  const [formData, setFormData] = useState<Partial<RegistrationRequest>>({
    participant_type: 'local',
    registration_type: 'individual',
    payment_method: 'cash',
    terms_accepted: false,
  });
  
  const [currentConference, setCurrentConference] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  useEffect(() => {
    const fetchCurrentConference = async () => {
      try {
        const response = await conferenceService.getCurrentConference();
        if (response.success && response.data) {
          setCurrentConference(response.data);
          setFormData(prev => ({ ...prev, conference_id: response.data.id }));
        }
      } catch (error) {
        console.error('Failed to fetch current conference:', error);
      }
    };

    fetchCurrentConference();
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    // Validation
    const requiredFields = [
      'full_name', 'email', 'phone', 'organization', 
      'position', 'country', 'conference_id'
    ];
    
    for (const field of requiredFields) {
      if (!formData[field as keyof RegistrationRequest]) {
        setError(`Please fill in the ${field.replace('_', ' ')} field`);
        return;
      }
    }

    if (!formData.terms_accepted) {
      setError('Please accept the terms and conditions');
      return;
    }

    setIsLoading(true);

    try {
      const response = await registrationService.submitRegistration(formData as RegistrationRequest);
      
      if (response.success && response.data) {
        setSuccess(`Registration successful! Your confirmation code is: ${response.data.confirmation_code}`);
        
        if (onSuccess) {
          onSuccess(response.data.confirmation_code);
        }
        
        // Reset form
        setFormData({
          participant_type: 'local',
          registration_type: 'individual',
          payment_method: 'cash',
          terms_accepted: false,
          conference_id: currentConference?.id,
        });
      } else {
        setError(response.message || 'Registration failed');
      }
    } catch (error: any) {
      setError(handleApiError(error));
    } finally {
      setIsLoading(false);
    }
  };

  if (!currentConference) {
    return (
      <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-md">
        <div className="text-center">Loading conference information...</div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6 text-center">Conference Registration</h2>
      
      <div className="mb-6 p-4 bg-orange-50 rounded-lg">
        <h3 className="font-semibold text-orange-800">{currentConference.title}</h3>
        <p className="text-sm text-orange-600">
          {new Date(currentConference.start_date).toLocaleDateString()} - {new Date(currentConference.end_date).toLocaleDateString()}
        </p>
        <p className="text-sm text-orange-600">{currentConference.location}</p>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      {success && (
        <div className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
          {success}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Full Name *
            </label>
            <input
              type="text"
              name="full_name"
              value={formData.full_name || ''}
              onChange={handleInputChange}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Email *
            </label>
            <input
              type="email"
              name="email"
              value={formData.email || ''}
              onChange={handleInputChange}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Phone *
            </label>
            <input
              type="tel"
              name="phone"
              value={formData.phone || ''}
              onChange={handleInputChange}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Organization *
            </label>
            <input
              type="text"
              name="organization"
              value={formData.organization || ''}
              onChange={handleInputChange}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Position *
            </label>
            <input
              type="text"
              name="position"
              value={formData.position || ''}
              onChange={handleInputChange}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Country *
            </label>
            <input
              type="text"
              name="country"
              value={formData.country || ''}
              onChange={handleInputChange}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Participant Type *
            </label>
            <select
              name="participant_type"
              value={formData.participant_type || 'local'}
              onChange={handleInputChange}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500"
              required
            >
              <option value="local">Local ({currentConference.currency_local} {currentConference.registration_fee_local})</option>
              <option value="international">International ({currentConference.currency_international} {currentConference.registration_fee_international})</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Registration Type *
            </label>
            <select
              name="registration_type"
              value={formData.registration_type || 'individual'}
              onChange={handleInputChange}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500"
              required
            >
              <option value="individual">Individual</option>
              <option value="group">Group</option>
            </select>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Dietary Requirements
          </label>
          <textarea
            name="dietary_requirements"
            value={formData.dietary_requirements || ''}
            onChange={handleInputChange}
            rows={3}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500"
            placeholder="Please specify any dietary requirements or allergies"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Special Needs
          </label>
          <textarea
            name="special_needs"
            value={formData.special_needs || ''}
            onChange={handleInputChange}
            rows={3}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500"
            placeholder="Please specify any special needs or accessibility requirements"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Payment Method *
          </label>
          <select
            name="payment_method"
            value={formData.payment_method || 'cash'}
            onChange={handleInputChange}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500"
            required
          >
            <option value="cash">Cash (Pay at venue)</option>
            <option value="bank_transfer">Bank Transfer</option>
            <option value="online">Online Payment</option>
          </select>
        </div>

        <div className="flex items-start space-x-2">
          <input
            type="checkbox"
            name="terms_accepted"
            checked={formData.terms_accepted || false}
            onChange={handleInputChange}
            className="mt-1"
            required
          />
          <label className="text-sm text-gray-700">
            I accept the terms and conditions and agree to participate in the conference *
          </label>
        </div>

        <button
          type="submit"
          disabled={isLoading}
          className="w-full bg-orange-500 hover:bg-orange-600 disabled:bg-gray-400 text-white py-3 px-4 rounded-md font-semibold transition-colors"
        >
          {isLoading ? 'Submitting Registration...' : 'Register Now'}
        </button>
      </form>
    </div>
  );
};

export default RegistrationForm;
