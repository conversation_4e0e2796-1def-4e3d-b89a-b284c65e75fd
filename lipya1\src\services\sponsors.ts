import { apiClient, ApiResponse } from '../lib/api';

export interface Sponsor {
  id: number;
  name: string;
  description: string;
  logo_url: string;
  website_url: string;
  sponsor_type: 'platinum' | 'gold' | 'silver' | 'bronze' | 'partner';
  is_active: boolean;
  order: number;
  conference_id: number;
}

export interface SponsorFilters {
  conference_id?: number;
  sponsor_type?: string;
  is_active?: boolean;
}

export const sponsorService = {
  // Get all sponsors
  async getSponsors(filters?: SponsorFilters): Promise<ApiResponse<Sponsor[]>> {
    return apiClient.get<Sponsor[]>('/sponsors', filters);
  },

  // Get specific sponsor by ID
  async getSponsor(id: number): Promise<ApiResponse<Sponsor>> {
    return apiClient.get<Sponsor>(`/sponsors/${id}`);
  }
};
