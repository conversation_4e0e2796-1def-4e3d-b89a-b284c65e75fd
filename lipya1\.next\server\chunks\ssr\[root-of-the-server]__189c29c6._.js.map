{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_e531dabc-module__QGiZLq__className\",\n  \"variable\": \"geist_e531dabc-module__QGiZLq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist%22,%22arguments%22:[{%22variable%22:%22--font-geist-sans%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_mono_68a01160-module__YLcDdW__className\",\n  \"variable\": \"geist_mono_68a01160-module__YLcDdW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist_Mono%22,%22arguments%22:[{%22variable%22:%22--font-geist-mono%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist Mono', 'Geist Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,0JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,0JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,0JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya/lipya1/src/components/Navbar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Navbar.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Navbar.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya/lipya1/src/components/Navbar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Navbar.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Navbar.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya/lipya1/src/components/Footer.tsx"], "sourcesContent": ["// components/Footer.js (أو .tsx إذا كنت تستخدم TypeScript)\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport { FaPhone, FaMapMarkerAlt, FaEnvelope } from 'react-icons/fa';\r\n\r\nconst Footer = () => {\r\n  return (\r\n    <footer className=\"bg-white text-gray-800 py-10 px-6 md:px-20 flex flex-col md:flex-row justify-between gap-10 border-t border-gray-200\">\r\n      {/* Left section */}\r\n      <div className=\"md:w-1/2\">\r\n        <div className=\"flex items-center mb-4\">\r\n          <img src=\"/logo.png\" alt=\"IREGO Logo\" className=\"h-20 w-auto mr-3\" />\r\n        </div>\r\n        <p className=\"text-sm text-gray-600 mb-4\">\r\n          Official website for the 2025 Tripoli conference on renewable energy, oil & gas, and climate change—\r\n          featuring event details, paper submissions, journals, and collaboration opportunities for global experts.\r\n        </p>\r\n\r\n        <div className=\"text-sm space-y-2 text-orange-600\">\r\n          <div className=\"flex items-center gap-2\">\r\n            <FaMapMarkerAlt /> \r\n            <span>Omar Al-Mukhtar Street, Tripoli - Libya</span>\r\n          </div>\r\n          <div className=\"flex items-center gap-2\">\r\n            <FaPhone />\r\n            <span>021884-444-4882</span>\r\n          </div>\r\n          <div className=\"flex items-center gap-2\">\r\n            <FaPhone />\r\n            <span>021884-444-4883</span>\r\n          </div>\r\n          <div className=\"flex items-center gap-2\">\r\n            <FaPhone />\r\n            <span>0021893-259-9265</span>\r\n          </div>\r\n          <div className=\"flex items-center gap-2\">\r\n            <FaEnvelope />\r\n            <span><EMAIL></span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Right section */}\r\n      <div className=\"md:w-1/2 grid grid-cols-2 gap-4 text-sm text-gray-700\">\r\n        <div className=\"space-y-2\">\r\n          <li><Link className=\"hover:text-orange-400 transition\" href=\"/\">Home</Link></li>\r\n\r\n          <Link href=\"/about\"><p className=\"cursor-pointer hover:text-orange-500\">About us</p></Link>\r\n          <Link href=\"/committees\"><p className=\"cursor-pointer hover:text-orange-500\">Committees\r\n</p></Link>\r\n          <Link href=\"/call-for-papers\"><p className=\"cursor-pointer hover:text-orange-500\">Call For Papers</p></Link>\r\n          <Link href=\"/submission\"><p className=\"cursor-pointer hover:text-orange-500\">Submission</p></Link>\r\n        </div>\r\n        <div className=\"space-y-2\">\r\n          <h4 className=\"font-semibold text-black\">Registration</h4>\r\n          <Link href=\"/venue\"><p className=\"cursor-pointer hover:text-orange-500\">Venue</p></Link>\r\n          <Link href=\"/contact\"><p className=\"cursor-pointer hover:text-orange-500\">Contact us</p></Link>\r\n          <Link href=\"/expo\"><p className=\"cursor-pointer hover:text-orange-500\">IREGO Expo</p></Link>\r\n          <Link href=\"/the-best\"><p className=\"cursor-pointer hover:text-orange-500\">IREGO The Best</p></Link>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n};\r\n\r\nexport default Footer;\r\n"], "names": [], "mappings": "AAAA,2DAA2D;;;;;AAE3D;AACA;;;;AAEA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,KAAI;4BAAY,KAAI;4BAAa,WAAU;;;;;;;;;;;kCAElD,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAK1C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8IAAA,CAAA,iBAAc;;;;;kDACf,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8IAAA,CAAA,UAAO;;;;;kDACR,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8IAAA,CAAA,UAAO;;;;;kDACR,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8IAAA,CAAA,UAAO;;;;;kDACR,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8IAAA,CAAA,aAAU;;;;;kDACX,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,WAAU;oCAAmC,MAAK;8CAAI;;;;;;;;;;;0CAEhE,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CAAS,cAAA,8OAAC;oCAAE,WAAU;8CAAuC;;;;;;;;;;;0CACxE,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CAAc,cAAA,8OAAC;oCAAE,WAAU;8CAAuC;;;;;;;;;;;0CAE7E,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CAAmB,cAAA,8OAAC;oCAAE,WAAU;8CAAuC;;;;;;;;;;;0CAClF,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CAAc,cAAA,8OAAC;oCAAE,WAAU;8CAAuC;;;;;;;;;;;;;;;;;kCAE/E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2B;;;;;;0CACzC,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CAAS,cAAA,8OAAC;oCAAE,WAAU;8CAAuC;;;;;;;;;;;0CACxE,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CAAW,cAAA,8OAAC;oCAAE,WAAU;8CAAuC;;;;;;;;;;;0CAC1E,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CAAQ,cAAA,8OAAC;oCAAE,WAAU;8CAAuC;;;;;;;;;;;0CACvE,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CAAY,cAAA,8OAAC;oCAAE,WAAU;8CAAuC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKrF;uCAEe", "debugId": null}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya/lipya1/src/components/ApiStatus.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ApiStatus.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ApiStatus.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgS,GAC7T,8DACA", "debugId": null}}, {"offset": {"line": 466, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya/lipya1/src/components/ApiStatus.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ApiStatus.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ApiStatus.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4Q,GACzS,0CACA", "debugId": null}}, {"offset": {"line": 480, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 490, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya/lipya1/src/context/UserContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const UserProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call UserProvider() from the server but UserProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/context/UserContext.tsx <module evaluation>\",\n    \"UserProvider\",\n);\nexport const useUser = registerClientReference(\n    function() { throw new Error(\"Attempted to call useUser() from the server but useUser is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/context/UserContext.tsx <module evaluation>\",\n    \"useUser\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,6DACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,6DACA", "debugId": null}}, {"offset": {"line": 508, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya/lipya1/src/context/UserContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const UserProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call UserProvider() from the server but UserProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/context/UserContext.tsx\",\n    \"UserProvider\",\n);\nexport const useUser = registerClientReference(\n    function() { throw new Error(\"Attempted to call useUser() from the server but useUser is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/context/UserContext.tsx\",\n    \"useUser\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,yCACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,yCACA", "debugId": null}}, {"offset": {"line": 526, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 536, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya/lipya1/src/app/layout.tsx"], "sourcesContent": ["import type { <PERSON>ada<PERSON> } from \"next\";\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from \"next/font/google\";\nimport \"./globals.css\";\nimport Navbar from \"../components/Navbar\";\nimport Footer from \"../components/Footer\";\nimport ApiStatus from \"../components/ApiStatus\";\nimport { UserProvider } from '../context/UserContext';\n\nconst geistSans = Geist({\n  variable: \"--font-geist-sans\",\n  subsets: [\"latin\"],\n});\n\nconst geistMono = Geist_Mono({\n  variable: \"--font-geist-mono\",\n  subsets: [\"latin\"],\n});\n\nexport const metadata: Metadata = {\n  title: \"International Conference 2025\",\n  description: \"Renewable Energy, Gas & Oil, and Climate Change Conference\",\n};\n\nexport default function RootLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  return (\n    <html lang=\"en\">\n      <body\n        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen flex flex-col`}\n      >\n        {/* ✅ الشريط البرتقالي المتحرك */}\n        <div className=\"bg-orange-600 overflow-hidden whitespace-nowrap\">\n          <div className=\"animate-marquee text-white text-sm py-2 px-4 inline-block\">\n            International Renewable Energy, Gas & Oil, and Climate Change Conference — November 25–27, 2025 — Tripoli, Libya\n          </div>\n        </div>\n\n        <UserProvider>\n          <Navbar />\n\n          <main className=\"flex-grow\">{children}</main>\n\n          <Footer />\n          <ApiStatus />\n        </UserProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAGA;AACA;AACA;AACA;;;;;;;;;AAYO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGT;IACC,qBACE,8OAAC;QAAK,MAAK;kBACT,cAAA,8OAAC;YACC,WAAW,GAAG,yIAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,8IAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,uCAAuC,CAAC;;8BAG/F,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCAA4D;;;;;;;;;;;8BAK7E,8OAAC,8HAAA,CAAA,eAAY;;sCACX,8OAAC,4HAAA,CAAA,UAAM;;;;;sCAEP,8OAAC;4BAAK,WAAU;sCAAa;;;;;;sCAE7B,8OAAC,4HAAA,CAAA,UAAM;;;;;sCACP,8OAAC,+HAAA,CAAA,UAAS;;;;;;;;;;;;;;;;;;;;;;AAKpB", "debugId": null}}]}