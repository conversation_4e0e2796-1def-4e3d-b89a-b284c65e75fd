<?php
/**
 * IREGO Conference Backend
 * Simple PHP API and Admin Panel
 */

// Check if this is an API request
$requestUri = $_SERVER['REQUEST_URI'];

if (strpos($requestUri, '/api/v1/') !== false) {
    include 'simple-api.php';
    exit();
}

if (strpos($requestUri, '/admin') !== false) {
    include 'admin-simple.php';
    exit();
}

// Default welcome page
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IREGO Conference Backend</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .welcome-card { background: white; border-radius: 15px; box-shadow: 0 15px 35px rgba(0,0,0,0.1); }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="welcome-card p-5">
                    <div class="text-center mb-4">
                        <h1 class="display-4 text-primary">IREGO Conference</h1>
                        <p class="lead">Backend API & Admin Panel</p>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-shield-alt fa-3x text-primary mb-3"></i>
                                    <h5>Admin Panel</h5>
                                    <p>Manage conferences, registrations, and content</p>
                                    <a href="/admin" class="btn btn-primary">
                                        <i class="fas fa-sign-in-alt me-2"></i>
                                        Access Admin
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-code fa-3x text-success mb-3"></i>
                                    <h5>API Documentation</h5>
                                    <p>RESTful API for frontend integration</p>
                                    <a href="#api-docs" class="btn btn-success">
                                        <i class="fas fa-book me-2"></i>
                                        View API Docs
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="api-docs" class="mt-5">
                        <h3>API Endpoints</h3>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Method</th>
                                        <th>Endpoint</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><span class="badge bg-success">GET</span></td>
                                        <td><code>/api/v1/conferences/current</code></td>
                                        <td>Get current active conference</td>
                                    </tr>
                                    <tr>
                                        <td><span class="badge bg-success">GET</span></td>
                                        <td><code>/api/v1/conferences</code></td>
                                        <td>Get all conferences</td>
                                    </tr>
                                    <tr>
                                        <td><span class="badge bg-primary">POST</span></td>
                                        <td><code>/api/v1/auth/login</code></td>
                                        <td>User authentication</td>
                                    </tr>
                                    <tr>
                                        <td><span class="badge bg-primary">POST</span></td>
                                        <td><code>/api/v1/auth/register</code></td>
                                        <td>User registration</td>
                                    </tr>
                                    <tr>
                                        <td><span class="badge bg-primary">POST</span></td>
                                        <td><code>/api/v1/registrations</code></td>
                                        <td>Conference registration</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <div class="mt-4 p-3 bg-light rounded">
                        <h6>Default Admin Credentials:</h6>
                        <p class="mb-0">
                            <strong>Email:</strong> <EMAIL><br>
                            <strong>Password:</strong> admin123
                        </p>
                    </div>
                    
                    <div class="mt-4 text-center">
                        <p class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            This is a simplified backend that works without Laravel dependencies
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
