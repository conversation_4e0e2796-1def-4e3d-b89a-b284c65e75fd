import { useState, useEffect } from 'react';
import { conferenceService, Conference } from '../services/conferences';
import { handleApiError } from '../lib/api';

export const useConference = () => {
  const [conference, setConference] = useState<Conference | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCurrentConference = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        const response = await conferenceService.getCurrentConference();
        
        if (response.success && response.data) {
          setConference(response.data);
        } else {
          setError(response.message || 'Failed to fetch conference data');
        }
      } catch (error: any) {
        setError(handleApiError(error));
      } finally {
        setIsLoading(false);
      }
    };

    fetchCurrentConference();
  }, []);

  return { conference, isLoading, error };
};
