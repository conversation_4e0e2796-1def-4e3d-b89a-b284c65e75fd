import { apiClient, ApiResponse } from '../lib/api';

export interface Speaker {
  id: number;
  name: string;
  title: string;
  bio: string;
  image_url: string;
  company: string;
  position: string;
  country: string;
  is_keynote: boolean;
  is_committee: boolean;
  is_featured: boolean;
  order: number;
  social_links?: {
    linkedin?: string;
    twitter?: string;
    website?: string;
  };
}

export interface SpeakerFilters {
  conference_id?: number;
  is_keynote?: boolean;
  is_committee?: boolean;
  is_featured?: boolean;
  per_page?: number;
  page?: number;
}

export const speakerService = {
  // Get all speakers
  async getSpeakers(filters?: SpeakerFilters): Promise<ApiResponse<Speaker[]>> {
    return apiClient.get<Speaker[]>('/speakers', filters);
  },

  // Get specific speaker by ID
  async getSpeaker(id: number): Promise<ApiResponse<Speaker>> {
    return apiClient.get<Speaker>(`/speakers/${id}`);
  },

  // Get committee members
  async getCommittees(): Promise<ApiResponse<Speaker[]>> {
    return apiClient.get<Speaker[]>('/speakers/committees/all');
  },

  // Get keynote speakers
  async getKeynotes(): Promise<ApiResponse<Speaker[]>> {
    return apiClient.get<Speaker[]>('/speakers/keynotes/all');
  },

  // Get featured speakers
  async getFeaturedSpeakers(): Promise<ApiResponse<Speaker[]>> {
    return apiClient.get<Speaker[]>('/speakers', { is_featured: true });
  }
};
