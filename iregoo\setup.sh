#!/bin/bash

echo "🚀 Setting up IREGO Conference Backend..."

# Install dependencies
echo "📦 Installing Composer dependencies..."
composer install

# Generate application key
echo "🔑 Generating application key..."
php artisan key:generate

# Generate JWT secret
echo "🔐 Generating JWT secret..."
php artisan jwt:secret

# Create database file if using SQLite
echo "🗄️ Setting up database..."
touch database/database.sqlite

# Run migrations
echo "📊 Running database migrations..."
php artisan migrate:fresh

# Seed database with sample data
echo "🌱 Seeding database with sample data..."
php artisan db:seed

# Create storage link
echo "🔗 Creating storage link..."
php artisan storage:link

# Clear caches
echo "🧹 Clearing caches..."
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear

echo "✅ Backend setup complete!"
echo ""
echo "🎯 Quick Start:"
echo "   Admin Panel: http://localhost:8000/admin"
echo "   API Base URL: http://localhost:8000/api/v1"
echo "   Default Admin: <EMAIL> / admin123"
echo ""
echo "🚀 To start the server:"
echo "   php artisan serve"
